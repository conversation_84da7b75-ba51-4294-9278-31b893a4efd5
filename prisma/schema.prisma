// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String?
  avatar      String?
  role        UserRole @default(DEVELOPER)
  expertise   String[] // Array of expertise areas
  availability UserAvailability @default(AVAILABLE)
  currentProjects Int @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  accounts    Account[]
  sessions    Session[]
  teamMemberships TeamMember[]
  leadingTeams Team[] @relation("TeamLead")
  assignedTasks Task[]
  createdTasks Task[] @relation("TaskCreator")
  documents   Document[]
  activities  Activity[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model Team {
  id          String   @id @default(cuid())
  name        String
  description String?
  module      HCMModule
  deadline    DateTime?
  status      TeamStatus @default(ACTIVE)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  leadId      String
  lead        User @relation("TeamLead", fields: [leadId], references: [id])
  members     TeamMember[]
  tasks       Task[]
  documents   Document[]
  activities  Activity[]

  @@map("teams")
}

model TeamMember {
  id       String @id @default(cuid())
  teamId   String
  userId   String
  role     TeamRole @default(MEMBER)
  joinedAt DateTime @default(now())

  team Team @relation(fields: [teamId], references: [id])
  user User @relation(fields: [userId], references: [id])

  @@unique([teamId, userId])
  @@map("team_members")
}

model Task {
  id              String   @id @default(cuid())
  title           String
  description     String?
  customer        String?
  priority        Priority @default(MEDIUM)
  status          TaskStatus @default(TODO)
  module          HCMModule
  dueDate         DateTime?
  estimatedHours  Int?
  completedHours  Int @default(0)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  assigneeId      String?
  assignee        User? @relation(fields: [assigneeId], references: [id])
  creatorId       String
  creator         User @relation("TaskCreator", fields: [creatorId], references: [id])
  teamId          String?
  team            Team? @relation(fields: [teamId], references: [id])
  documents       Document[]
  activities      Activity[]

  @@map("tasks")
}

model Document {
  id          String   @id @default(cuid())
  title       String
  description String?
  content     String?
  type        DocumentType
  module      HCMModule?
  tags        String[]
  isPublic    Boolean @default(false)
  filePath    String?
  fileSize    Int?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  authorId    String
  author      User @relation(fields: [authorId], references: [id])
  teamId      String?
  team        Team? @relation(fields: [teamId], references: [id])
  taskId      String?
  task        Task? @relation(fields: [taskId], references: [id])

  @@map("documents")
}

model Activity {
  id        String   @id @default(cuid())
  type      ActivityType
  message   String
  module    String?
  createdAt DateTime @default(now())

  // Relations
  userId    String
  user      User @relation(fields: [userId], references: [id])
  teamId    String?
  team      Team? @relation(fields: [teamId], references: [id])
  taskId    String?
  task      Task? @relation(fields: [taskId], references: [id])

  @@map("activities")
}

// Enums
enum UserRole {
  ADMIN
  TEAM_LEAD
  SENIOR_DEVELOPER
  DEVELOPER
  ANALYST
}

enum UserAvailability {
  AVAILABLE
  BUSY
  VACATION
}

enum TeamStatus {
  ACTIVE
  INACTIVE
  COMPLETED
  ARCHIVED
}

enum TeamRole {
  LEAD
  MEMBER
  CONTRIBUTOR
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  CODE_REVIEW
  TESTING
  COMPLETED
  BLOCKED
}

enum HCMModule {
  PA  // Personnel Administration
  PT  // Time Management
  PY  // Payroll
  PB  // Benefits
  RC  // Recruitment
  OM  // Organizational Management
  PD  // Personnel Development
}

enum DocumentType {
  SPECIFICATION
  GUIDE
  REVIEW
  TEMPLATE
  CODE_DOCUMENTATION
  API_DOCUMENTATION
}

enum ActivityType {
  TEAM_CREATED
  TEAM_UPDATED
  MEMBER_ADDED
  MEMBER_REMOVED
  TASK_CREATED
  TASK_UPDATED
  TASK_COMPLETED
  DOCUMENT_CREATED
  DOCUMENT_UPDATED
  CODE_COMMITTED
  INTEGRATION_STATUS
}

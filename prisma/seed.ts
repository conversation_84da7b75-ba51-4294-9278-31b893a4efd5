import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create sample users
  const users = await Promise.all([
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '<PERSON>',
        avatar: '/placeholder-user.jpg',
        role: 'TEAM_LEAD',
        expertise: ['ABAP', 'HCM', 'Payroll', 'Team Management'],
        availability: 'AVAILABLE',
        currentProjects: 2,
      },
    }),
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '<PERSON>',
        avatar: '/placeholder-user.jpg',
        role: 'SENIOR_DEVELOPER',
        expertise: ['ABAP', 'Time Management', 'Workflow', 'Integration'],
        availability: 'AVAILABLE',
        currentProjects: 1,
      },
    }),
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '<PERSON>',
        avatar: '/placeholder-user.jpg',
        role: 'DEVELOPER',
        expertise: ['ABAP', 'Benefits', 'Reports', 'Forms'],
        availability: 'BUSY',
        currentProjects: 3,
      },
    }),
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Lisa Wilson',
        avatar: '/placeholder-user.jpg',
        role: 'DEVELOPER',
        expertise: ['ABAP', 'Personnel Administration', 'Data Migration'],
        availability: 'AVAILABLE',
        currentProjects: 1,
      },
    }),
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Tom Brown',
        avatar: '/placeholder-user.jpg',
        role: 'ANALYST',
        expertise: ['Requirements Analysis', 'Testing', 'Documentation'],
        availability: 'AVAILABLE',
        currentProjects: 2,
      },
    }),
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Emma Garcia',
        avatar: '/placeholder-user.jpg',
        role: 'SENIOR_DEVELOPER',
        expertise: ['ABAP', 'Recruitment', 'Organizational Management'],
        availability: 'VACATION',
        currentProjects: 0,
      },
    }),
  ])

  console.log('✅ Created users:', users.length)

  // Create sample teams
  const team1 = await prisma.team.create({
    data: {
      name: 'Payroll Enhancement Squad',
      description: 'Focused on improving payroll calculation logic and performance',
      module: 'PY',
      deadline: new Date('2024-03-15'),
      status: 'ACTIVE',
      leadId: users[0].id, // John Smith
    },
  })

  const team2 = await prisma.team.create({
    data: {
      name: 'Time Management Innovators',
      description: 'Developing new time tracking and management features',
      module: 'PT',
      deadline: new Date('2024-02-28'),
      status: 'ACTIVE',
      leadId: users[1].id, // Sarah Johnson
    },
  })

  console.log('✅ Created teams:', 2)

  // Add team members
  await Promise.all([
    // Team 1 members
    prisma.teamMember.create({
      data: {
        teamId: team1.id,
        userId: users[0].id, // John Smith (Lead)
        role: 'LEAD',
      },
    }),
    prisma.teamMember.create({
      data: {
        teamId: team1.id,
        userId: users[2].id, // Mike Davis
        role: 'MEMBER',
      },
    }),
    prisma.teamMember.create({
      data: {
        teamId: team1.id,
        userId: users[4].id, // Tom Brown
        role: 'CONTRIBUTOR',
      },
    }),
    // Team 2 members
    prisma.teamMember.create({
      data: {
        teamId: team2.id,
        userId: users[1].id, // Sarah Johnson (Lead)
        role: 'LEAD',
      },
    }),
    prisma.teamMember.create({
      data: {
        teamId: team2.id,
        userId: users[3].id, // Lisa Wilson
        role: 'MEMBER',
      },
    }),
  ])

  console.log('✅ Created team memberships')

  // Create sample activities
  await Promise.all([
    prisma.activity.create({
      data: {
        type: 'TEAM_CREATED',
        message: 'Team "Payroll Enhancement Squad" was created',
        userId: users[0].id,
        teamId: team1.id,
        module: 'PY',
      },
    }),
    prisma.activity.create({
      data: {
        type: 'TEAM_CREATED',
        message: 'Team "Time Management Innovators" was created',
        userId: users[1].id,
        teamId: team2.id,
        module: 'PT',
      },
    }),
    prisma.activity.create({
      data: {
        type: 'MEMBER_ADDED',
        message: 'Mike Davis joined the team',
        userId: users[2].id,
        teamId: team1.id,
        module: 'PY',
      },
    }),
  ])

  console.log('✅ Created activities')
  console.log('🎉 Seeding completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

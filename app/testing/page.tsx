"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import {
  Play,
  RefreshCw,
  CheckCircle2,
  AlertTriangle,
  Clock,
  Target,
  BarChart3,
  FileText,
  Settings,
} from "lucide-react"

interface TestSuite {
  id: string
  name: string
  description: string
  status: "idle" | "running" | "passed" | "failed"
  tests: number
  passed: number
  failed: number
  duration: string
  coverage: number
  lastRun: string
}

interface TestResult {
  id: string
  name: string
  status: "passed" | "failed" | "skipped"
  duration: string
  message?: string
  suite: string
}

export default function TestingPage() {
  const [testSuites, setTestSuites] = useState<TestSuite[]>([
    {
      id: "payroll-tests",
      name: "Payroll Calculation Tests",
      description: "Unit tests for payroll schema calculations",
      status: "passed",
      tests: 15,
      passed: 14,
      failed: 1,
      duration: "2.3s",
      coverage: 92,
      lastRun: "5 minutes ago",
    },
    {
      id: "time-mgmt-tests",
      name: "Time Management Tests",
      description: "Integration tests for time recording",
      status: "running",
      tests: 8,
      passed: 6,
      failed: 0,
      duration: "1.8s",
      coverage: 88,
      lastRun: "Running...",
    },
    {
      id: "benefits-tests",
      name: "Benefits Administration Tests",
      description: "End-to-end tests for benefits calculation",
      status: "failed",
      tests: 12,
      passed: 9,
      failed: 3,
      duration: "3.1s",
      coverage: 75,
      lastRun: "1 hour ago",
    },
  ])

  const [testResults, setTestResults] = useState<TestResult[]>([
    {
      id: "test-1",
      name: "should calculate basic salary correctly",
      status: "passed",
      duration: "0.12s",
      suite: "payroll-tests",
    },
    {
      id: "test-2",
      name: "should handle overtime calculations",
      status: "failed",
      duration: "0.08s",
      message: "Expected 1200.00 but got 1150.00",
      suite: "payroll-tests",
    },
    {
      id: "test-3",
      name: "should validate employee data",
      status: "passed",
      duration: "0.05s",
      suite: "payroll-tests",
    },
    {
      id: "test-4",
      name: "should record time entries correctly",
      status: "passed",
      duration: "0.15s",
      suite: "time-mgmt-tests",
    },
    {
      id: "test-5",
      name: "should calculate benefits premium",
      status: "failed",
      duration: "0.22s",
      message: "Database connection timeout",
      suite: "benefits-tests",
    },
  ])

  const [isRunningAll, setIsRunningAll] = useState(false)
  const [selectedSuite, setSelectedSuite] = useState<string | null>(null)

  // Simulate test execution
  const runTestSuite = (suiteId: string) => {
    setTestSuites((prev) =>
      prev.map((suite) =>
        suite.id === suiteId ? { ...suite, status: "running" as const, lastRun: "Running..." } : suite,
      ),
    )

    setTimeout(() => {
      setTestSuites((prev) =>
        prev.map((suite) =>
          suite.id === suiteId
            ? {
                ...suite,
                status: Math.random() > 0.3 ? ("passed" as const) : ("failed" as const),
                lastRun: "Just now",
                passed: Math.floor(Math.random() * suite.tests),
                failed: suite.tests - Math.floor(Math.random() * suite.tests),
              }
            : suite,
        ),
      )
    }, 3000)
  }

  const runAllTests = () => {
    setIsRunningAll(true)
    testSuites.forEach((suite) => runTestSuite(suite.id))

    setTimeout(() => {
      setIsRunningAll(false)
    }, 5000)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "passed":
        return <CheckCircle2 className="w-4 h-4 text-green-600" />
      case "failed":
        return <AlertTriangle className="w-4 h-4 text-red-600" />
      case "running":
        return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
      default:
        return <Clock className="w-4 h-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "passed":
        return "bg-green-100 text-green-800"
      case "failed":
        return "bg-red-100 text-red-800"
      case "running":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const totalTests = testSuites.reduce((sum, suite) => sum + suite.tests, 0)
  const totalPassed = testSuites.reduce((sum, suite) => sum + suite.passed, 0)
  const totalFailed = testSuites.reduce((sum, suite) => sum + suite.failed, 0)
  const overallCoverage = Math.round(testSuites.reduce((sum, suite) => sum + suite.coverage, 0) / testSuites.length)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Automated Testing</h1>
          <p className="text-muted-foreground">Comprehensive testing framework for ABAP code quality assurance</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" disabled={isRunningAll}>
            <Settings className="w-4 h-4 mr-2" />
            Configure
          </Button>
          <Button onClick={runAllTests} disabled={isRunningAll}>
            {isRunningAll ? <RefreshCw className="w-4 h-4 mr-2 animate-spin" /> : <Play className="w-4 h-4 mr-2" />}
            {isRunningAll ? "Running All..." : "Run All Tests"}
          </Button>
        </div>
      </div>

      {/* Test Overview */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Tests</p>
                <p className="text-2xl font-bold">{totalTests}</p>
              </div>
              <Target className="w-4 h-4 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Passed</p>
                <p className="text-2xl font-bold text-green-600">{totalPassed}</p>
              </div>
              <CheckCircle2 className="w-4 h-4 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Failed</p>
                <p className="text-2xl font-bold text-red-600">{totalFailed}</p>
              </div>
              <AlertTriangle className="w-4 h-4 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Coverage</p>
                <p className="text-2xl font-bold">{overallCoverage}%</p>
              </div>
              <BarChart3 className="w-4 h-4 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="suites" className="space-y-6">
        <TabsList>
          <TabsTrigger value="suites">Test Suites</TabsTrigger>
          <TabsTrigger value="results">Test Results</TabsTrigger>
          <TabsTrigger value="coverage">Coverage Report</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="suites" className="space-y-4">
          {testSuites.map((suite) => (
            <Card key={suite.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(suite.status)}
                      <h3 className="font-semibold">{suite.name}</h3>
                      <Badge className={getStatusColor(suite.status)}>{suite.status}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{suite.description}</p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => runTestSuite(suite.id)}
                      disabled={suite.status === "running"}
                    >
                      {suite.status === "running" ? (
                        <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                      ) : (
                        <Play className="w-3 h-3 mr-1" />
                      )}
                      Run
                    </Button>
                    <Button variant="ghost" size="sm">
                      <FileText className="w-3 h-3 mr-1" />
                      Details
                    </Button>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Tests</p>
                    <p className="font-medium">{suite.tests}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Passed</p>
                    <p className="font-medium text-green-600">{suite.passed}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Failed</p>
                    <p className="font-medium text-red-600">{suite.failed}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Duration</p>
                    <p className="font-medium">{suite.duration}</p>
                  </div>
                </div>

                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Coverage</span>
                    <span>{suite.coverage}%</span>
                  </div>
                  <Progress value={suite.coverage} className="h-2" />
                  <p className="text-xs text-muted-foreground">Last run: {suite.lastRun}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>Detailed results from the latest test runs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {testResults.map((result) => (
                  <div key={result.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(result.status)}
                      <div>
                        <p className="font-medium text-sm">{result.name}</p>
                        <p className="text-xs text-muted-foreground">{result.suite}</p>
                        {result.message && <p className="text-xs text-red-600 mt-1">{result.message}</p>}
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge className={getStatusColor(result.status)}>{result.status}</Badge>
                      <p className="text-xs text-muted-foreground mt-1">{result.duration}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="coverage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Code Coverage Report</CardTitle>
              <CardDescription>Line and branch coverage analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {testSuites.map((suite) => (
                  <div key={suite.id} className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{suite.name}</h4>
                      <Badge variant="outline">{suite.coverage}%</Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Line Coverage</span>
                        <span>{suite.coverage}%</span>
                      </div>
                      <Progress value={suite.coverage} className="h-2" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Branch Coverage</span>
                        <span>{Math.max(0, suite.coverage - 5)}%</span>
                      </div>
                      <Progress value={Math.max(0, suite.coverage - 5)} className="h-2" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
              <CardDescription>Test execution performance and trends</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <p className="text-sm font-medium">Average Test Duration</p>
                  <p className="text-2xl font-bold">0.15s</p>
                  <p className="text-xs text-green-600">↓ 12% faster than last week</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Total Execution Time</p>
                  <p className="text-2xl font-bold">7.2s</p>
                  <p className="text-xs text-muted-foreground">For {totalTests} tests</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Success Rate</p>
                  <p className="text-2xl font-bold">{Math.round((totalPassed / totalTests) * 100)}%</p>
                  <p className="text-xs text-green-600">↑ 5% improvement</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter, Plus, FileText, Calendar, User, Eye, Edit, Trash2 } from "lucide-react"
import Link from "next/link"

interface Document {
  id: string
  title: string
  description?: string
  type: string
  module?: string
  tags: string[]
  isPublic: boolean
  createdAt: string
  updatedAt: string
  author: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  team?: {
    id: string
    name: string
    module: string
  }
  task?: {
    id: string
    title: string
    status: string
  }
}

export default function DocumentsPage() {
  const [documents, setDocuments] = useState<Document[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("")
  const [moduleFilter, setModuleFilter] = useState("")

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        const params = new URLSearchParams()
        if (typeFilter) params.append('type', typeFilter)
        if (moduleFilter) params.append('module', moduleFilter)
        if (searchTerm) params.append('search', searchTerm)
        
        const response = await fetch(`/api/documents?${params.toString()}`)
        if (response.ok) {
          const data = await response.json()
          setDocuments(data.documents || [])
        } else {
          console.error('Failed to fetch documents')
          setDocuments([])
        }
      } catch (error) {
        console.error('Error fetching documents:', error)
        setDocuments([])
      } finally {
        setLoading(false)
      }
    }

    fetchDocuments()
  }, [typeFilter, moduleFilter, searchTerm])

  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      SPECIFICATION: 'bg-blue-100 text-blue-800',
      GUIDE: 'bg-green-100 text-green-800',
      REVIEW: 'bg-purple-100 text-purple-800',
      TEMPLATE: 'bg-orange-100 text-orange-800',
      CODE_DOCUMENTATION: 'bg-indigo-100 text-indigo-800',
      API_DOCUMENTATION: 'bg-pink-100 text-pink-800',
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
  }

  const getModuleColor = (module: string) => {
    const colors: Record<string, string> = {
      PA: 'bg-blue-100 text-blue-800',
      PT: 'bg-green-100 text-green-800',
      PY: 'bg-purple-100 text-purple-800',
      PB: 'bg-orange-100 text-orange-800',
      RC: 'bg-red-100 text-red-800',
      OM: 'bg-indigo-100 text-indigo-800',
      PD: 'bg-pink-100 text-pink-800',
    }
    return colors[module] || 'bg-gray-100 text-gray-800'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Documents</h1>
          <p className="text-muted-foreground">Manage technical documentation and knowledge base</p>
        </div>
        <Button asChild>
          <Link href="/documents/new">
            <Plus className="w-4 h-4 mr-2" />
            Create Document
          </Link>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search documents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Types</SelectItem>
                <SelectItem value="SPECIFICATION">Technical Specification</SelectItem>
                <SelectItem value="GUIDE">Implementation Guide</SelectItem>
                <SelectItem value="REVIEW">Code Review</SelectItem>
                <SelectItem value="API_DOCUMENTATION">API Documentation</SelectItem>
                <SelectItem value="CODE_DOCUMENTATION">Code Documentation</SelectItem>
                <SelectItem value="TEMPLATE">Template</SelectItem>
              </SelectContent>
            </Select>
            <Select value={moduleFilter} onValueChange={setModuleFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Modules" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Modules</SelectItem>
                <SelectItem value="PA">Personnel Administration</SelectItem>
                <SelectItem value="PT">Time Management</SelectItem>
                <SelectItem value="PY">Payroll</SelectItem>
                <SelectItem value="PB">Benefits</SelectItem>
                <SelectItem value="RC">Recruitment</SelectItem>
                <SelectItem value="OM">Organizational Management</SelectItem>
                <SelectItem value="PD">Personnel Development</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Documents Grid */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-muted-foreground">Loading documents...</div>
        </div>
      ) : documents.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No documents found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchTerm || typeFilter || moduleFilter
                ? "Try adjusting your search or filter criteria"
                : "Get started by creating your first document"
              }
            </p>
            {!searchTerm && !typeFilter && !moduleFilter && (
              <Button asChild>
                <Link href="/documents/new">
                  <Plus className="w-4 h-4 mr-2" />
                  Create First Document
                </Link>
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {documents.map((document) => (
            <Card key={document.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1">
                    <CardTitle className="text-lg line-clamp-2">{document.title}</CardTitle>
                    <div className="flex items-center gap-2 flex-wrap">
                      <Badge className={getTypeColor(document.type)}>
                        {document.type.replace('_', ' ')}
                      </Badge>
                      {document.module && (
                        <Badge className={getModuleColor(document.module)}>
                          {document.module}
                        </Badge>
                      )}
                      {document.isPublic && (
                        <Badge variant="outline">Public</Badge>
                      )}
                    </div>
                  </div>
                </div>
                {document.description && (
                  <CardDescription className="line-clamp-2">
                    {document.description}
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Author */}
                  <div className="flex items-center gap-3">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={document.author.avatar || "/placeholder-user.jpg"} />
                      <AvatarFallback className="text-xs">
                        {document.author.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{document.author.name}</p>
                      <p className="text-xs text-muted-foreground">Author</p>
                    </div>
                  </div>

                  {/* Tags */}
                  {document.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {document.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {document.tags.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{document.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}

                  {/* Metadata */}
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(document.createdAt)}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Eye className="w-4 h-4 mr-2" />
                      View
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}

"use client"

import React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { FileText, BookOpen, Code2, Settings, Save, Eye, Upload, Plus, X, Users, Calendar, Tag } from "lucide-react"

interface DocumentTemplate {
  id: string
  name: string
  description: string
  type: string
  content: string
  sections: string[]
}

export default function NewDocumentPage() {
  const [documentTitle, setDocumentTitle] = useState("")
  const [documentDescription, setDocumentDescription] = useState("")
  const [documentType, setDocumentType] = useState("")
  const [module, setModule] = useState("")
  const [content, setContent] = useState("")
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState("")
  const [selectedTemplate, setSelectedTemplate] = useState("")
  const [isPublic, setIsPublic] = useState(false)
  const [assignedTeam, setAssignedTeam] = useState("")
  const [assignedTask, setAssignedTask] = useState("")
  const [templates, setTemplates] = useState<DocumentTemplate[]>([])
  const [teams, setTeams] = useState<any[]>([])
  const [tasks, setTasks] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch templates
        const templatesResponse = await fetch('/api/documents/templates')
        if (templatesResponse.ok) {
          const templatesData = await templatesResponse.json()
          setTemplates(templatesData.templates || [])
        }

        // Fetch teams
        const teamsResponse = await fetch('/api/teams')
        if (teamsResponse.ok) {
          const teamsData = await teamsResponse.json()
          setTeams(teamsData.teams || [])
        }

      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])



  const documentTypes = [
    { value: "SPECIFICATION", label: "Technical Specification", icon: FileText },
    { value: "GUIDE", label: "Implementation Guide", icon: BookOpen },
    { value: "REVIEW", label: "Code Review", icon: Code2 },
    { value: "API_DOCUMENTATION", label: "API Documentation", icon: Code2 },
    { value: "CODE_DOCUMENTATION", label: "Code Documentation", icon: Code2 },
    { value: "TEMPLATE", label: "Template", icon: Settings },
  ]



  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find((t) => t.id === templateId)
    if (template) {
      setSelectedTemplate(templateId)
      setDocumentType(template.type)
      setContent(template.content)
      if (!documentTitle) {
        setDocumentTitle(template.name)
      }
    }
  }

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag("")
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove))
  }

  const handleSaveDocument = async () => {
    if (!documentTitle || !documentType) {
      alert('Please fill in the required fields (title and type)')
      return
    }

    setSaving(true)
    try {
      const response = await fetch('/api/documents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: documentTitle,
          description: documentDescription,
          content,
          type: documentType,
          module: module || undefined,
          tags,
          isPublic,
          authorId: 'user-1', // TODO: Get from auth context
          teamId: assignedTeam || undefined,
          taskId: assignedTask || undefined,
        }),
      })

      if (response.ok) {
        const newDocument = await response.json()
        console.log('Document created successfully:', newDocument)
        // Redirect to document view or show success message
        window.location.href = `/documents/${newDocument.id}`
      } else {
        const error = await response.json()
        console.error('Failed to create document:', error)
        alert('Failed to create document: ' + error.error)
      }
    } catch (error) {
      console.error('Error creating document:', error)
      alert('Error creating document. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Create New Document</h1>
          <p className="text-muted-foreground">Create technical documentation for your HCM development projects</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => window.history.back()}>
            Cancel
          </Button>
          <Button variant="outline">
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button onClick={handleSaveDocument} disabled={saving}>
            <Save className="w-4 h-4 mr-2" />
            {saving ? 'Saving...' : 'Save Document'}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="create" className="space-y-6">
        <TabsList>
          <TabsTrigger value="create">Create Document</TabsTrigger>
          <TabsTrigger value="templates">Use Template</TabsTrigger>
          <TabsTrigger value="upload">Upload Document</TabsTrigger>
        </TabsList>

        <TabsContent value="create" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-3">
            {/* Document Configuration */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Document Information
                  </CardTitle>
                  <CardDescription>Define the basic details of your document</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="doc-title">Document Title *</Label>
                    <Input
                      id="doc-title"
                      placeholder="e.g., Payroll Schema Enhancement Specification"
                      value={documentTitle}
                      onChange={(e) => setDocumentTitle(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="doc-description">Description *</Label>
                    <Textarea
                      id="doc-description"
                      placeholder="Brief description of the document's purpose and content..."
                      value={documentDescription}
                      onChange={(e) => setDocumentDescription(e.target.value)}
                      rows={3}
                    />
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="doc-type">Document Type *</Label>
                      <Select value={documentType} onValueChange={setDocumentType}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select document type" />
                        </SelectTrigger>
                        <SelectContent>
                          {documentTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              <div className="flex items-center gap-2">
                                <type.icon className="w-4 h-4" />
                                {type.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="module">HCM Module</Label>
                      <Select value={module} onValueChange={setModule}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select HCM module" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="PY">Payroll (PY)</SelectItem>
                          <SelectItem value="PT">Time Management (PT)</SelectItem>
                          <SelectItem value="PB">Benefits (PB)</SelectItem>
                          <SelectItem value="PA">Personnel Administration (PA)</SelectItem>
                          <SelectItem value="RC">Recruitment (RC)</SelectItem>
                          <SelectItem value="General">General/Cross-Module</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="space-y-2">
                    <Label>Tags</Label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add a tag..."
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && handleAddTag()}
                      />
                      <Button type="button" onClick={handleAddTag}>
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    {tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                            <Tag className="w-3 h-3" />
                            {tag}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveTag(tag)}
                              className="h-4 w-4 p-0 ml-1"
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Content Editor */}
              <Card>
                <CardHeader>
                  <CardTitle>Document Content</CardTitle>
                  <CardDescription>Write your document content using Markdown syntax</CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Start writing your document content here..."
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    className="min-h-[400px] font-mono text-sm"
                  />
                </CardContent>
              </Card>
            </div>

            {/* Document Settings */}
            <div className="space-y-6">
              {/* Visibility Settings */}
              <Card>
                <CardHeader>
                  <CardTitle>Visibility & Access</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="public" checked={isPublic} onCheckedChange={(checked) => setIsPublic(checked === true)} />
                    <Label htmlFor="public">Make document public</Label>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="team">Assign to Team</Label>
                    <Select value={assignedTeam} onValueChange={setAssignedTeam}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select team (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        {teams.map((team) => (
                          <SelectItem key={team.id} value={team.id}>
                            {team.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* Document Stats */}
              <Card>
                <CardHeader>
                  <CardTitle>Document Stats</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Word Count:</span>
                    <span className="font-medium">{content.split(/\s+/).filter(Boolean).length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Character Count:</span>
                    <span className="font-medium">{content.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Lines:</span>
                    <span className="font-medium">{content.split("\n").length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Estimated Read Time:</span>
                    <span className="font-medium">
                      {Math.max(1, Math.ceil(content.split(/\s+/).filter(Boolean).length / 200))} min
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <Upload className="w-4 h-4 mr-2" />
                    Import from File
                  </Button>
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <Users className="w-4 h-4 mr-2" />
                    Share with Team
                  </Button>
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule Review
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Document Templates</CardTitle>
              <CardDescription>Choose from pre-built templates to get started quickly</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-sm text-muted-foreground">Loading templates...</div>
                </div>
              ) : templates.length === 0 ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-sm text-muted-foreground">No templates available</div>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {templates.map((template) => (
                  <Card
                    key={template.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedTemplate === template.id ? "ring-2 ring-primary" : ""
                    }`}
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          {documentTypes.find((t) => t.value === template.type)?.icon && (
                            <div className="w-5 h-5">
                              {React.createElement(documentTypes.find((t) => t.value === template.type)!.icon, {
                                className: "w-5 h-5 text-muted-foreground",
                              })}
                            </div>
                          )}
                          <h4 className="font-medium">{template.name}</h4>
                        </div>
                        <p className="text-sm text-muted-foreground">{template.description}</p>
                        <div className="flex flex-wrap gap-1">
                          {template.sections.slice(0, 3).map((section) => (
                            <Badge key={section} variant="secondary" className="text-xs">
                              {section}
                            </Badge>
                          ))}
                          {template.sections.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{template.sections.length - 3}
                            </Badge>
                          )}
                        </div>
                        <Button
                          size="sm"
                          className="w-full"
                          variant={selectedTemplate === template.id ? "default" : "outline"}
                        >
                          {selectedTemplate === template.id ? "Selected" : "Use Template"}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upload" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Upload Document</CardTitle>
              <CardDescription>Upload an existing document file</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                <Upload className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Upload Document</h3>
                <p className="text-muted-foreground mb-4">Drag and drop your document here, or click to browse files</p>
                <p className="text-sm text-muted-foreground mb-4">
                  Supported formats: PDF, DOC, DOCX, MD, TXT (Max 10MB)
                </p>
                <Button>
                  <Upload className="w-4 h-4 mr-2" />
                  Browse Files
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

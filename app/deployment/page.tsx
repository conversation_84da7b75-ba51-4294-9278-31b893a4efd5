"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Rocket,
  CheckCircle2,
  AlertTriangle,
  Clock,
  RefreshCw,
  Server,
  GitBranch,
  Activity,
  Shield,
  Zap,
} from "lucide-react"

interface Deployment {
  id: string
  name: string
  environment: "DEV" | "QAS" | "PRD"
  status: "pending" | "running" | "success" | "failed" | "rollback"
  progress: number
  startTime: string
  duration: string
  deployedBy: string
  version: string
  changes: string[]
}

interface Environment {
  name: string
  status: "online" | "offline" | "maintenance"
  health: number
  lastDeployment: string
  version: string
  uptime: string
}

export default function DeploymentPage() {
  const [deployments, setDeployments] = useState<Deployment[]>([
    {
      id: "deploy-001",
      name: "Payroll Schema Update",
      environment: "QAS",
      status: "running",
      progress: 65,
      startTime: "14:30",
      duration: "3m 45s",
      deployedBy: "John Smith",
      version: "v2.1.3",
      changes: ["Updated payroll calculation logic", "Fixed overtime calculation bug", "Added new tax brackets"],
    },
    {
      id: "deploy-002",
      name: "Time Management Enhancement",
      environment: "DEV",
      status: "success",
      progress: 100,
      startTime: "13:15",
      duration: "2m 12s",
      deployedBy: "Sarah Johnson",
      version: "v1.8.2",
      changes: ["Improved time entry validation", "Added shift differential support"],
    },
    {
      id: "deploy-003",
      name: "Benefits Module Hotfix",
      environment: "PRD",
      status: "failed",
      progress: 45,
      startTime: "12:00",
      duration: "1m 30s",
      deployedBy: "Mike Davis",
      version: "v3.0.1",
      changes: ["Critical bug fix for benefits calculation"],
    },
  ])

  const [environments, setEnvironments] = useState<Environment[]>([
    {
      name: "Development (DEV)",
      status: "online",
      health: 98,
      lastDeployment: "2 hours ago",
      version: "v1.8.2",
      uptime: "99.9%",
    },
    {
      name: "Quality Assurance (QAS)",
      status: "maintenance",
      health: 85,
      lastDeployment: "Running...",
      version: "v2.1.2",
      uptime: "99.5%",
    },
    {
      name: "Production (PRD)",
      status: "online",
      health: 99,
      lastDeployment: "1 day ago",
      version: "v3.0.0",
      uptime: "99.99%",
    },
  ])

  const [selectedEnvironment, setSelectedEnvironment] = useState("QAS")
  const [isDeploying, setIsDeploying] = useState(false)

  // Simulate deployment progress
  useEffect(() => {
    const interval = setInterval(() => {
      setDeployments((prev) =>
        prev.map((deployment) =>
          deployment.status === "running"
            ? {
                ...deployment,
                progress: Math.min(100, deployment.progress + Math.random() * 10),
                duration: `${Math.floor(Math.random() * 5) + 3}m ${Math.floor(Math.random() * 60)}s`,
              }
            : deployment,
        ),
      )
    }, 2000)

    return () => clearInterval(interval)
  }, [])

  const startDeployment = () => {
    setIsDeploying(true)
    const newDeployment: Deployment = {
      id: `deploy-${Date.now()}`,
      name: "New Feature Deployment",
      environment: selectedEnvironment as "DEV" | "QAS" | "PRD",
      status: "running",
      progress: 0,
      startTime: new Date().toLocaleTimeString(),
      duration: "0s",
      deployedBy: "Current User",
      version: "v2.2.0",
      changes: ["New feature implementation", "Performance improvements"],
    }

    setDeployments((prev) => [newDeployment, ...prev])

    setTimeout(() => {
      setIsDeploying(false)
      setDeployments((prev) =>
        prev.map((d) => (d.id === newDeployment.id ? { ...d, status: "success" as const, progress: 100 } : d)),
      )
    }, 8000)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle2 className="w-4 h-4 text-green-600" />
      case "failed":
        return <AlertTriangle className="w-4 h-4 text-red-600" />
      case "running":
        return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
      case "rollback":
        return <RefreshCw className="w-4 h-4 text-orange-600" />
      default:
        return <Clock className="w-4 h-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "bg-green-100 text-green-800"
      case "failed":
        return "bg-red-100 text-red-800"
      case "running":
        return "bg-blue-100 text-blue-800"
      case "rollback":
        return "bg-orange-100 text-orange-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getEnvironmentStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-green-100 text-green-800"
      case "offline":
        return "bg-red-100 text-red-800"
      case "maintenance":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Deployment Management</h1>
          <p className="text-muted-foreground">Automated deployment pipeline for SAP HCM environments</p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={selectedEnvironment} onValueChange={setSelectedEnvironment}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select environment" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="DEV">Development</SelectItem>
              <SelectItem value="QAS">Quality Assurance</SelectItem>
              <SelectItem value="PRD">Production</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={startDeployment} disabled={isDeploying}>
            {isDeploying ? <RefreshCw className="w-4 h-4 mr-2 animate-spin" /> : <Rocket className="w-4 h-4 mr-2" />}
            {isDeploying ? "Deploying..." : "Deploy"}
          </Button>
        </div>
      </div>

      {/* Environment Status */}
      <div className="grid gap-4 md:grid-cols-3">
        {environments.map((env, index) => (
          <Card key={index} className="relative overflow-hidden">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Server className="w-4 h-4" />
                  <span className="font-medium">{env.name}</span>
                </div>
                <Badge className={getEnvironmentStatusColor(env.status)}>{env.status}</Badge>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Health</span>
                  <span>{env.health}%</span>
                </div>
                <Progress value={env.health} className="h-2" />

                <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                  <div>Version: {env.version}</div>
                  <div>Uptime: {env.uptime}</div>
                  <div className="col-span-2">Last: {env.lastDeployment}</div>
                </div>
              </div>
            </CardContent>
            <div
              className={`absolute bottom-0 left-0 w-full h-1 ${
                env.status === "online" ? "bg-green-500" : env.status === "maintenance" ? "bg-yellow-500" : "bg-red-500"
              }`}
            />
          </Card>
        ))}
      </div>

      <Tabs defaultValue="deployments" className="space-y-6">
        <TabsList>
          <TabsTrigger value="deployments">Deployments</TabsTrigger>
          <TabsTrigger value="pipeline">Pipeline</TabsTrigger>
          <TabsTrigger value="rollback">Rollback</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="deployments" className="space-y-4">
          {deployments.map((deployment) => (
            <Card key={deployment.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(deployment.status)}
                      <h3 className="font-semibold">{deployment.name}</h3>
                      <Badge variant="outline">{deployment.environment}</Badge>
                      <Badge className={getStatusColor(deployment.status)}>{deployment.status}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Version {deployment.version} • Deployed by {deployment.deployedBy}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    {deployment.status === "failed" && (
                      <Button variant="outline" size="sm">
                        <RefreshCw className="w-3 h-3 mr-1" />
                        Rollback
                      </Button>
                    )}
                    <Button variant="ghost" size="sm">
                      <Activity className="w-3 h-3 mr-1" />
                      Logs
                    </Button>
                  </div>
                </div>

                {deployment.status === "running" && (
                  <div className="mb-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Deployment Progress</span>
                      <span>{Math.round(deployment.progress)}%</span>
                    </div>
                    <Progress value={deployment.progress} className="h-2" />
                  </div>
                )}

                <div className="grid gap-4 md:grid-cols-3 mb-4">
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Start Time</p>
                    <p className="font-medium">{deployment.startTime}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Duration</p>
                    <p className="font-medium">{deployment.duration}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Environment</p>
                    <p className="font-medium">{deployment.environment}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium">Changes Deployed:</p>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    {deployment.changes.map((change, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                        {change}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="pipeline" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="w-5 h-5" />
                Deployment Pipeline
              </CardTitle>
              <CardDescription>Automated CI/CD pipeline stages</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {[
                  { stage: "Code Commit", status: "completed", duration: "0.5s" },
                  { stage: "Build & Compile", status: "completed", duration: "45s" },
                  { stage: "Unit Tests", status: "completed", duration: "1m 20s" },
                  { stage: "Code Analysis", status: "running", duration: "30s" },
                  { stage: "Deploy to QAS", status: "pending", duration: "-" },
                  { stage: "Integration Tests", status: "pending", duration: "-" },
                  { stage: "Deploy to PRD", status: "pending", duration: "-" },
                ].map((stage, index) => (
                  <div key={index} className="flex items-center gap-4">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full border-2 border-muted">
                      {stage.status === "completed" ? (
                        <CheckCircle2 className="w-4 h-4 text-green-600" />
                      ) : stage.status === "running" ? (
                        <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
                      ) : (
                        <Clock className="w-4 h-4 text-gray-400" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{stage.stage}</span>
                        <div className="flex items-center gap-2">
                          <Badge className={getStatusColor(stage.status)}>{stage.status}</Badge>
                          <span className="text-sm text-muted-foreground">{stage.duration}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rollback" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCw className="w-5 h-5" />
                Rollback Management
              </CardTitle>
              <CardDescription>Manage deployment rollbacks and version history</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { version: "v3.0.1", date: "2024-01-15 12:00", status: "current", environment: "PRD" },
                  { version: "v3.0.0", date: "2024-01-14 16:30", status: "previous", environment: "PRD" },
                  { version: "v2.9.8", date: "2024-01-13 14:15", status: "stable", environment: "PRD" },
                  { version: "v2.9.7", date: "2024-01-12 10:45", status: "archived", environment: "PRD" },
                ].map((version, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline">{version.environment}</Badge>
                      <div>
                        <p className="font-medium">{version.version}</p>
                        <p className="text-sm text-muted-foreground">{version.date}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        className={
                          version.status === "current"
                            ? "bg-green-100 text-green-800"
                            : version.status === "previous"
                              ? "bg-blue-100 text-blue-800"
                              : version.status === "stable"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-gray-100 text-gray-800"
                        }
                      >
                        {version.status}
                      </Badge>
                      {version.status !== "current" && (
                        <Button variant="outline" size="sm">
                          <RefreshCw className="w-3 h-3 mr-1" />
                          Rollback
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Security Monitoring
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">SSL Certificate</span>
                    <Badge className="bg-green-100 text-green-800">Valid</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Access Control</span>
                    <Badge className="bg-green-100 text-green-800">Active</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Vulnerability Scan</span>
                    <Badge className="bg-green-100 text-green-800">Clean</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Audit Logging</span>
                    <Badge className="bg-green-100 text-green-800">Enabled</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Response Time</span>
                    <span className="font-medium">45ms</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Throughput</span>
                    <span className="font-medium">1,247 req/min</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Error Rate</span>
                    <span className="font-medium text-green-600">0.02%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">CPU Usage</span>
                    <span className="font-medium">23%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

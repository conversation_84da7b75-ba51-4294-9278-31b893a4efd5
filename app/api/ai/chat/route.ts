import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// Schema for chat requests
const chatRequestSchema = z.object({
  message: z.string().min(1, 'Message is required'),
  conversationId: z.string().optional(),
  context: z.object({
    module: z.enum(['PA', 'PT', 'PY', 'PB', 'RC', 'OM', 'PD']).optional(),
    currentCode: z.string().optional(),
    userRole: z.string().optional(),
  }).optional(),
})

// Mock conversation storage (in production, use a database)
const conversations = new Map<string, Array<{ role: 'user' | 'assistant', content: string, timestamp: string }>>()

// ABAP knowledge base for contextual responses
const abapKnowledgeBase = {
  concepts: {
    'internal table': {
      definition: 'Internal tables are temporary data structures in ABAP that exist only during program execution.',
      syntax: 'DATA: lt_table TYPE TABLE OF structure_type.',
      examples: [
        'DATA: lt_employees TYPE TABLE OF pa0001.',
        'APPEND ls_employee TO lt_employees.',
        'LOOP AT lt_employees INTO ls_employee.'
      ]
    },
    'select statement': {
      definition: 'SELECT statements retrieve data from database tables.',
      syntax: 'SELECT fields FROM table WHERE conditions.',
      examples: [
        'SELECT * FROM pa0001 WHERE pernr = \'12345\'.',
        'SELECT pernr, ename FROM pa0001 INTO TABLE lt_data.',
        'SELECT SINGLE * FROM pa0001 WHERE pernr = lv_pernr.'
      ]
    },
    'class definition': {
      definition: 'ABAP classes encapsulate data and methods in object-oriented programming.',
      syntax: 'CLASS class_name DEFINITION ... ENDCLASS.',
      examples: [
        'CLASS zcl_employee DEFINITION PUBLIC FINAL.',
        'METHODS: get_employee_data IMPORTING iv_pernr TYPE pernr_d.',
        'DATA: mv_employee_id TYPE pernr_d.'
      ]
    }
  },
  hcm_modules: {
    'PA': {
      name: 'Personnel Administration',
      description: 'Manages employee master data, organizational assignments, and personal information.',
      key_infotypes: ['0001 - Organizational Assignment', '0002 - Personal Data', '0006 - Addresses'],
      common_tasks: ['Employee hiring', 'Organizational changes', 'Address updates']
    },
    'PT': {
      name: 'Time Management',
      description: 'Handles time recording, attendance, and absence management.',
      key_infotypes: ['2001 - Absences', '2002 - Attendances', '2003 - Substitutions'],
      common_tasks: ['Time recording', 'Leave management', 'Overtime calculation']
    },
    'PY': {
      name: 'Payroll',
      description: 'Processes payroll calculations, wage types, and payment processing.',
      key_tables: ['PCL1', 'PCL2', 'T512T - Wage Types'],
      common_tasks: ['Payroll run', 'Wage type configuration', 'Tax calculations']
    }
  },
  best_practices: [
    'Always use authorization checks when accessing employee data',
    'Implement proper error handling with TRY-CATCH blocks',
    'Use modern ABAP syntax like inline declarations and constructor expressions',
    'Optimize database access with FOR ALL ENTRIES or JOINs',
    'Follow naming conventions: Z/Y prefix for custom objects'
  ]
}

// POST /api/ai/chat - Handle chat conversations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = chatRequestSchema.parse(body)

    const conversationId = validatedData.conversationId || generateConversationId()
    const userMessage = validatedData.message
    const context = validatedData.context

    // Get or create conversation history
    if (!conversations.has(conversationId)) {
      conversations.set(conversationId, [])
    }
    const conversation = conversations.get(conversationId)!

    // Add user message to conversation
    conversation.push({
      role: 'user',
      content: userMessage,
      timestamp: new Date().toISOString()
    })

    // Generate AI response
    const aiResponse = generateChatResponse(userMessage, conversation, context)

    // Add AI response to conversation
    conversation.push({
      role: 'assistant',
      content: aiResponse.content,
      timestamp: new Date().toISOString()
    })

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1500))

    return NextResponse.json({
      conversationId,
      response: aiResponse,
      context: context,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error processing chat request:', error)
    return NextResponse.json(
      { error: 'Failed to process chat request' },
      { status: 500 }
    )
  }
}

function generateConversationId(): string {
  return 'conv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

function generateChatResponse(message: string, conversation: any[], context: any) {
  const messageLower = message.toLowerCase()

  // Greeting responses
  if (messageLower.includes('hello') || messageLower.includes('hi')) {
    return {
      content: "Hello! I'm your ABAP AI assistant. I can help you with:\n\n• ABAP code generation and optimization\n• HCM module-specific guidance\n• Best practices and code reviews\n• Debugging and troubleshooting\n\nWhat would you like to work on today?",
      type: 'greeting',
      suggestions: [
        'Generate a simple ABAP report',
        'Explain HCM infotypes',
        'Review my ABAP code',
        'Optimize database queries'
      ]
    }
  }

  // HCM module questions
  if (messageLower.includes('hcm') || messageLower.includes('module')) {
    const moduleMatch = Object.keys(abapKnowledgeBase.hcm_modules).find(module => 
      messageLower.includes(module.toLowerCase()) || 
      messageLower.includes(abapKnowledgeBase.hcm_modules[module as keyof typeof abapKnowledgeBase.hcm_modules].name.toLowerCase())
    )

    if (moduleMatch) {
      const moduleInfo = abapKnowledgeBase.hcm_modules[moduleMatch as keyof typeof abapKnowledgeBase.hcm_modules]
      return {
        content: `**${moduleInfo.name} (${moduleMatch})**\n\n${moduleInfo.description}\n\n**Key Components:**\n${moduleInfo.key_infotypes ? moduleInfo.key_infotypes.map(item => `• ${item}`).join('\n') : moduleInfo.key_tables?.map(item => `• ${item}`).join('\n')}\n\n**Common Tasks:**\n${moduleInfo.common_tasks.map(task => `• ${task}`).join('\n')}`,
        type: 'module_info',
        suggestions: [
          `Show ${moduleMatch} code examples`,
          `Best practices for ${moduleMatch}`,
          `Common ${moduleMatch} issues`
        ]
      }
    }
  }

  // Code generation requests
  if (messageLower.includes('generate') || messageLower.includes('create') || messageLower.includes('write')) {
    if (messageLower.includes('report')) {
      return {
        content: "I'll help you generate an ABAP report. Here's a template:\n\n```abap\nREPORT z_your_report.\n\n\" Selection screen\nSELECTION-SCREEN BEGIN OF BLOCK b1 WITH FRAME TITLE text-001.\n  PARAMETERS: p_param TYPE string.\nSELECTION-SCREEN END OF BLOCK b1.\n\nSTART-OF-SELECTION.\n  \" Your logic here\n  WRITE: 'Report executed successfully'.\n```\n\nWould you like me to customize this for a specific HCM module or requirement?",
        type: 'code_generation',
        suggestions: [
          'Add employee data selection',
          'Include ALV display',
          'Add authorization checks'
        ]
      }
    }

    if (messageLower.includes('class')) {
      return {
        content: "Here's a basic ABAP class template:\n\n```abap\nCLASS zcl_your_class DEFINITION\n  PUBLIC\n  FINAL\n  CREATE PUBLIC.\n\n  PUBLIC SECTION.\n    METHODS: constructor,\n             your_method IMPORTING iv_param TYPE string.\n\n  PRIVATE SECTION.\n    DATA: mv_attribute TYPE string.\nENDCLASS.\n\nCLASS zcl_your_class IMPLEMENTATION.\n  METHOD constructor.\n    \" Initialization logic\n  ENDMETHOD.\n\n  METHOD your_method.\n    \" Method implementation\n  ENDMETHOD.\nENDCLASS.\n```\n\nWhat specific functionality should this class handle?",
        type: 'code_generation',
        suggestions: [
          'Add HCM data processing',
          'Include error handling',
          'Add unit test methods'
        ]
      }
    }
  }

  // Code review and optimization
  if (messageLower.includes('review') || messageLower.includes('optimize') || messageLower.includes('improve')) {
    return {
      content: "I'd be happy to review your ABAP code! Please share your code and I'll analyze it for:\n\n• **Performance** - Database access optimization, memory usage\n• **Security** - Authorization checks, input validation\n• **Best Practices** - Naming conventions, error handling\n• **Maintainability** - Code structure, documentation\n\nYou can paste your code in the next message, and I'll provide detailed feedback.",
      type: 'code_review',
      suggestions: [
        'Check for performance issues',
        'Validate security practices',
        'Suggest modern ABAP syntax'
      ]
    }
  }

  // ABAP concept explanations
  const conceptMatch = Object.keys(abapKnowledgeBase.concepts).find(concept => 
    messageLower.includes(concept.replace(' ', ''))
  )

  if (conceptMatch) {
    const concept = abapKnowledgeBase.concepts[conceptMatch as keyof typeof abapKnowledgeBase.concepts]
    return {
      content: `**${conceptMatch.toUpperCase()}**\n\n${concept.definition}\n\n**Syntax:**\n\`\`\`abap\n${concept.syntax}\n\`\`\`\n\n**Examples:**\n${concept.examples.map(ex => `\`\`\`abap\n${ex}\n\`\`\``).join('\n\n')}`,
      type: 'concept_explanation',
      suggestions: [
        'Show more examples',
        'Explain best practices',
        'Common mistakes to avoid'
      ]
    }
  }

  // Best practices
  if (messageLower.includes('best practice') || messageLower.includes('recommendation')) {
    return {
      content: `**ABAP Best Practices:**\n\n${abapKnowledgeBase.best_practices.map(practice => `• ${practice}`).join('\n')}\n\nWould you like me to elaborate on any of these practices or provide specific examples?`,
      type: 'best_practices',
      suggestions: [
        'Show authorization check examples',
        'Explain modern ABAP syntax',
        'Database optimization techniques'
      ]
    }
  }

  // Default response
  return {
    content: `I understand you're asking about: "${message}"\n\nI can help you with various ABAP and HCM development tasks. Could you be more specific about what you'd like to accomplish? For example:\n\n• Code generation (reports, classes, functions)\n• Code review and optimization\n• HCM module guidance\n• ABAP concept explanations\n• Best practices and troubleshooting`,
    type: 'clarification',
    suggestions: [
      'Generate ABAP code',
      'Explain HCM concepts',
      'Review existing code',
      'Show best practices'
    ]
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// Schema for code analysis requests
const analysisRequestSchema = z.object({
  code: z.string().min(1, 'Code is required'),
  analysisType: z.enum(['performance', 'security', 'quality', 'comprehensive']).default('comprehensive'),
  context: z.object({
    module: z.enum(['PA', 'PT', 'PY', 'PB', 'RC', 'OM', 'PD']).optional(),
    objectType: z.enum(['program', 'class', 'function', 'report', 'interface']).optional(),
  }).optional(),
})

// ABAP code analysis patterns and rules
const analysisRules = {
  performance: [
    {
      pattern: /SELECT\s+\*\s+FROM/gi,
      severity: 'High',
      category: 'Database Performance',
      message: 'Avoid SELECT * - specify only required fields',
      suggestion: 'Use SELECT field1, field2 FROM table instead of SELECT *'
    },
    {
      pattern: /LOOP\s+AT\s+\w+.*SELECT/gi,
      severity: 'Critical',
      category: 'Database Performance',
      message: 'SELECT statement inside LOOP - major performance issue',
      suggestion: 'Use FOR ALL ENTRIES or JOIN to avoid SELECT in LOOP'
    },
    {
      pattern: /APPEND\s+\w+\s+TO\s+\w+\./gi,
      severity: 'Medium',
      category: 'Memory Performance',
      message: 'Consider using APPEND LINES for bulk operations',
      suggestion: 'Use APPEND LINES OF source_table TO target_table for better performance'
    },
    {
      pattern: /READ\s+TABLE\s+\w+\s+WITH\s+KEY/gi,
      severity: 'Medium',
      category: 'Table Performance',
      message: 'Consider using sorted tables or binary search',
      suggestion: 'Use SORT table or READ TABLE WITH KEY ... BINARY SEARCH'
    }
  ],
  security: [
    {
      pattern: /AUTHORITY-CHECK/gi,
      severity: 'Info',
      category: 'Authorization',
      message: 'Authorization check found - good practice',
      suggestion: 'Ensure all sensitive operations have authorization checks'
    },
    {
      pattern: /(SELECT|UPDATE|DELETE|INSERT).*WHERE.*=\s*['"]/gi,
      severity: 'High',
      category: 'SQL Injection',
      message: 'Potential SQL injection risk with string literals',
      suggestion: 'Use parameterized queries or proper escaping'
    },
    {
      pattern: /CALL\s+FUNCTION\s+['"]RFC_/gi,
      severity: 'Medium',
      category: 'Remote Function Call',
      message: 'RFC call detected - ensure proper error handling',
      suggestion: 'Add exception handling for RFC calls'
    }
  ],
  quality: [
    {
      pattern: /DATA:\s*\w+\s*TYPE\s*C\s*LENGTH\s*\d+/gi,
      severity: 'Low',
      category: 'Data Types',
      message: 'Consider using STRING instead of fixed-length CHAR',
      suggestion: 'Use DATA: variable TYPE string for dynamic text'
    },
    {
      pattern: /IF\s+SY-SUBRC\s*=\s*0/gi,
      severity: 'Info',
      category: 'Error Handling',
      message: 'Good practice: checking SY-SUBRC',
      suggestion: 'Continue using SY-SUBRC checks after database operations'
    },
    {
      pattern: /WRITE:/gi,
      severity: 'Low',
      category: 'Output',
      message: 'Consider using ALV for better data presentation',
      suggestion: 'Use ALV (ABAP List Viewer) for professional data display'
    },
    {
      pattern: /CONCATENATE/gi,
      severity: 'Low',
      category: 'String Operations',
      message: 'Consider using string templates',
      suggestion: 'Use |{ var1 } { var2 }| instead of CONCATENATE'
    }
  ]
}

// HCM-specific analysis rules
const hcmRules = {
  PA: [
    {
      pattern: /PA\d{4}/gi,
      severity: 'Info',
      category: 'HCM Infotype',
      message: 'Personnel Administration infotype detected',
      suggestion: 'Ensure proper authorization checks for employee data'
    }
  ],
  PT: [
    {
      pattern: /(CAT\d|2\d{3})/gi,
      severity: 'Info',
      category: 'Time Management',
      message: 'Time management data detected',
      suggestion: 'Consider time zone handling for global organizations'
    }
  ],
  PY: [
    {
      pattern: /(PCL[12]|\/\d{3})/gi,
      severity: 'Medium',
      category: 'Payroll',
      message: 'Payroll cluster access detected',
      suggestion: 'Ensure proper payroll period validation and authorization'
    }
  ]
}

// POST /api/ai/analyze - Analyze ABAP code
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = analysisRequestSchema.parse(body)

    const { code, analysisType, context } = validatedData

    // Perform analysis based on type
    const analysisResult = analyzeCode(code, analysisType, context)

    // Simulate AI processing time
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2000))

    return NextResponse.json({
      success: true,
      analysis: analysisResult,
      timestamp: new Date().toISOString(),
      codeLength: code.length,
      linesOfCode: code.split('\n').length
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error analyzing code:', error)
    return NextResponse.json(
      { error: 'Failed to analyze code' },
      { status: 500 }
    )
  }
}

function analyzeCode(code: string, analysisType: string, context: any) {
  const issues: any[] = []
  const metrics = calculateMetrics(code)
  const suggestions: string[] = []

  // Apply analysis rules based on type
  const rulesToApply = analysisType === 'comprehensive' 
    ? [...analysisRules.performance, ...analysisRules.security, ...analysisRules.quality]
    : analysisRules[analysisType as keyof typeof analysisRules] || []

  // Check against general rules
  rulesToApply.forEach(rule => {
    const matches = code.match(rule.pattern)
    if (matches) {
      matches.forEach(match => {
        const lineNumber = getLineNumber(code, match)
        issues.push({
          type: rule.category,
          severity: rule.severity,
          message: rule.message,
          suggestion: rule.suggestion,
          line: lineNumber,
          code: match.trim()
        })
      })
    }
  })

  // Apply HCM-specific rules if context is provided
  if (context?.module && hcmRules[context.module as keyof typeof hcmRules]) {
    const hcmSpecificRules = hcmRules[context.module as keyof typeof hcmRules]
    hcmSpecificRules.forEach(rule => {
      const matches = code.match(rule.pattern)
      if (matches) {
        matches.forEach(match => {
          const lineNumber = getLineNumber(code, match)
          issues.push({
            type: rule.category,
            severity: rule.severity,
            message: rule.message,
            suggestion: rule.suggestion,
            line: lineNumber,
            code: match.trim()
          })
        })
      }
    })
  }

  // Generate overall suggestions
  if (issues.some(issue => issue.type === 'Database Performance')) {
    suggestions.push('Consider implementing database optimization techniques')
  }
  if (issues.some(issue => issue.severity === 'Critical')) {
    suggestions.push('Address critical issues before deployment')
  }
  if (metrics.complexity > 10) {
    suggestions.push('Consider breaking down complex methods into smaller functions')
  }

  // Calculate quality score
  const qualityScore = calculateQualityScore(issues, metrics)

  return {
    summary: generateSummary(issues, metrics),
    qualityScore,
    metrics,
    issues: issues.sort((a, b) => getSeverityWeight(b.severity) - getSeverityWeight(a.severity)),
    suggestions,
    recommendations: generateRecommendations(issues, context)
  }
}

function calculateMetrics(code: string) {
  const lines = code.split('\n')
  const codeLines = lines.filter(line => line.trim() && !line.trim().startsWith('*'))
  const commentLines = lines.filter(line => line.trim().startsWith('*'))
  
  return {
    totalLines: lines.length,
    codeLines: codeLines.length,
    commentLines: commentLines.length,
    commentRatio: commentLines.length / Math.max(codeLines.length, 1),
    complexity: estimateComplexity(code),
    maintainabilityIndex: calculateMaintainabilityIndex(code)
  }
}

function estimateComplexity(code: string): number {
  const complexityPatterns = [
    /IF\s+/gi,
    /ELSEIF\s+/gi,
    /CASE\s+/gi,
    /WHEN\s+/gi,
    /LOOP\s+/gi,
    /WHILE\s+/gi,
    /DO\s+/gi
  ]
  
  let complexity = 1 // Base complexity
  complexityPatterns.forEach(pattern => {
    const matches = code.match(pattern)
    if (matches) {
      complexity += matches.length
    }
  })
  
  return complexity
}

function calculateMaintainabilityIndex(code: string): number {
  const metrics = calculateMetrics(code)
  // Simplified maintainability index calculation
  const baseScore = 100
  const complexityPenalty = metrics.complexity * 2
  const commentBonus = metrics.commentRatio * 10
  
  return Math.max(0, Math.min(100, baseScore - complexityPenalty + commentBonus))
}

function getLineNumber(code: string, match: string): number {
  const beforeMatch = code.substring(0, code.indexOf(match))
  return beforeMatch.split('\n').length
}

function getSeverityWeight(severity: string): number {
  const weights = { 'Critical': 4, 'High': 3, 'Medium': 2, 'Low': 1, 'Info': 0 }
  return weights[severity as keyof typeof weights] || 0
}

function calculateQualityScore(issues: any[], metrics: any): number {
  let score = 100
  
  issues.forEach(issue => {
    const penalty = getSeverityWeight(issue.severity) * 5
    score -= penalty
  })
  
  // Bonus for good practices
  if (metrics.commentRatio > 0.2) score += 5
  if (metrics.complexity < 5) score += 5
  
  return Math.max(0, Math.min(100, score))
}

function generateSummary(issues: any[], metrics: any): string {
  const criticalIssues = issues.filter(i => i.severity === 'Critical').length
  const highIssues = issues.filter(i => i.severity === 'High').length
  
  if (criticalIssues > 0) {
    return `Code analysis found ${criticalIssues} critical issue(s) that need immediate attention.`
  } else if (highIssues > 0) {
    return `Code analysis found ${highIssues} high-priority issue(s) that should be addressed.`
  } else if (issues.length > 0) {
    return `Code analysis found ${issues.length} issue(s) with opportunities for improvement.`
  } else {
    return 'Code analysis completed successfully with no major issues found.'
  }
}

function generateRecommendations(issues: any[], context: any): string[] {
  const recommendations = []
  
  if (issues.some(i => i.type === 'Database Performance')) {
    recommendations.push('Optimize database access patterns for better performance')
  }
  
  if (issues.some(i => i.type === 'Authorization')) {
    recommendations.push('Implement comprehensive authorization checks')
  }
  
  if (context?.module === 'PY') {
    recommendations.push('Ensure payroll calculations follow regulatory compliance')
  }
  
  recommendations.push('Add comprehensive unit tests for all methods')
  recommendations.push('Document complex business logic with inline comments')
  
  return recommendations
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/users - Get all users (for team member selection)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const role = searchParams.get('role')
    const availability = searchParams.get('availability')
    const expertise = searchParams.get('expertise')

    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (role) {
      where.role = role
    }

    if (availability) {
      where.availability = availability
    }

    if (expertise) {
      where.expertise = {
        has: expertise,
      }
    }

    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        role: true,
        expertise: true,
        availability: true,
        currentProjects: true,
        _count: {
          select: {
            teamMemberships: true,
            assignedTasks: true,
          },
        },
      },
      orderBy: [
        { availability: 'asc' }, // Available users first
        { currentProjects: 'asc' }, // Less busy users first
        { name: 'asc' },
      ],
    })

    return NextResponse.json(users)
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

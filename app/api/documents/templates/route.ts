import { NextRequest, NextResponse } from 'next/server'

// Document templates with predefined content
const documentTemplates = [
  {
    id: 'technical-specification',
    name: 'Technical Specification',
    description: 'Comprehensive technical specification template for HCM development projects',
    type: 'SPECIFICATION',
    sections: [
      'Overview',
      'Requirements',
      'Technical Architecture',
      'Implementation Details',
      'Testing Strategy',
      'Deployment Plan'
    ],
    content: `# Technical Specification

## 1. Overview
### Project Description
[Provide a brief description of the project and its objectives]

### Scope
[Define what is included and excluded from this specification]

### Stakeholders
- **Business Owner:** [Name]
- **Technical Lead:** [Name]
- **Development Team:** [Team Name]

## 2. Requirements
### Functional Requirements
- [Requirement 1]
- [Requirement 2]
- [Requirement 3]

### Non-Functional Requirements
- **Performance:** [Performance criteria]
- **Security:** [Security requirements]
- **Scalability:** [Scalability requirements]

## 3. Technical Architecture
### System Overview
[High-level system architecture description]

### HCM Module Integration
- **Module:** [PA/PT/PY/PB/RC/OM/PD]
- **Integration Points:** [List integration points]
- **Data Flow:** [Describe data flow]

### Technology Stack
- **Backend:** SAP ABAP
- **Database:** SAP HANA/Database
- **Integration:** [APIs, RFCs, etc.]

## 4. Implementation Details
### Development Approach
[Describe the development methodology and approach]

### Key Components
1. **Component 1:** [Description]
2. **Component 2:** [Description]
3. **Component 3:** [Description]

### ABAP Objects/Programs
- [List of programs, classes, function modules]

## 5. Testing Strategy
### Unit Testing
[Unit testing approach and coverage]

### Integration Testing
[Integration testing scenarios]

### User Acceptance Testing
[UAT criteria and process]

## 6. Deployment Plan
### Deployment Steps
1. [Step 1]
2. [Step 2]
3. [Step 3]

### Rollback Plan
[Rollback procedures if needed]

### Go-Live Checklist
- [ ] Code review completed
- [ ] Unit tests passed
- [ ] Integration tests passed
- [ ] UAT sign-off received
- [ ] Documentation updated
`
  },
  {
    id: 'user-guide',
    name: 'User Guide',
    description: 'User guide template for end-user documentation',
    type: 'GUIDE',
    sections: [
      'Introduction',
      'Getting Started',
      'Features',
      'Step-by-Step Instructions',
      'Troubleshooting',
      'FAQ'
    ],
    content: `# User Guide

## 1. Introduction
### Purpose
[Explain the purpose of this guide and the system/feature it covers]

### Audience
[Define the target audience for this guide]

### Prerequisites
[List any prerequisites or requirements]

## 2. Getting Started
### System Access
[How to access the system]

### Navigation
[Basic navigation instructions]

### User Interface Overview
[Overview of the main interface elements]

## 3. Features
### Feature 1: [Feature Name]
[Description of the feature and its benefits]

### Feature 2: [Feature Name]
[Description of the feature and its benefits]

### Feature 3: [Feature Name]
[Description of the feature and its benefits]

## 4. Step-by-Step Instructions
### Task 1: [Task Name]
1. [Step 1]
2. [Step 2]
3. [Step 3]

### Task 2: [Task Name]
1. [Step 1]
2. [Step 2]
3. [Step 3]

## 5. Troubleshooting
### Common Issues
**Issue:** [Problem description]
**Solution:** [Solution steps]

**Issue:** [Problem description]
**Solution:** [Solution steps]

## 6. FAQ
**Q:** [Question]
**A:** [Answer]

**Q:** [Question]
**A:** [Answer]

## 7. Support
For additional support, contact:
- **Help Desk:** [Contact information]
- **Technical Support:** [Contact information]
`
  },
  {
    id: 'code-review',
    name: 'Code Review',
    description: 'Code review template for ABAP development',
    type: 'REVIEW',
    sections: [
      'Review Summary',
      'Code Quality',
      'Performance',
      'Security',
      'Recommendations',
      'Approval'
    ],
    content: `# Code Review Report

## Review Information
- **Developer:** [Developer Name]
- **Reviewer:** [Reviewer Name]
- **Review Date:** [Date]
- **HCM Module:** [PA/PT/PY/PB/RC/OM/PD]
- **Transport Request:** [Transport Number]

## Code Components Reviewed
- [ ] Programs
- [ ] Function Modules
- [ ] Classes
- [ ] Interfaces
- [ ] Data Dictionary Objects

## Review Criteria

### 1. Code Quality
- [ ] **Naming Conventions:** Variables, methods, and classes follow SAP naming conventions
- [ ] **Documentation:** Code is properly documented with comments
- [ ] **Structure:** Code is well-structured and readable
- [ ] **Error Handling:** Proper error handling implemented

**Comments:**
[Detailed comments on code quality]

### 2. Performance
- [ ] **Database Access:** Efficient database queries
- [ ] **Memory Usage:** Optimal memory usage
- [ ] **Processing Logic:** Efficient algorithms and logic

**Comments:**
[Performance-related comments]

### 3. Security
- [ ] **Authorization Checks:** Proper authorization implemented
- [ ] **Input Validation:** User inputs are validated
- [ ] **Data Protection:** Sensitive data is protected

**Comments:**
[Security-related comments]

### 4. SAP Standards
- [ ] **Coding Standards:** Follows SAP ABAP coding standards
- [ ] **Best Practices:** Implements SAP best practices
- [ ] **Integration:** Proper integration with HCM modules

**Comments:**
[Standards compliance comments]

## Issues Found
### Critical Issues
1. [Issue description and location]
2. [Issue description and location]

### Major Issues
1. [Issue description and location]
2. [Issue description and location]

### Minor Issues
1. [Issue description and location]
2. [Issue description and location]

## Recommendations
1. [Recommendation 1]
2. [Recommendation 2]
3. [Recommendation 3]

## Review Decision
- [ ] **Approved:** Code is ready for deployment
- [ ] **Approved with Minor Changes:** Code can be deployed after addressing minor issues
- [ ] **Requires Major Changes:** Code needs significant modifications before approval
- [ ] **Rejected:** Code requires complete rework

**Reviewer Signature:** [Reviewer Name]
**Date:** [Date]
`
  },
  {
    id: 'api-documentation',
    name: 'API Documentation',
    description: 'API documentation template for web services and interfaces',
    type: 'API_DOCUMENTATION',
    sections: [
      'Overview',
      'Authentication',
      'Endpoints',
      'Data Models',
      'Examples',
      'Error Handling'
    ],
    content: `# API Documentation

## Overview
### Description
[Brief description of the API and its purpose]

### Base URL
\`\`\`
https://api.company.com/hcm/v1
\`\`\`

### Version
Current Version: v1.0

## Authentication
### Authentication Method
[Describe authentication method - API Key, OAuth, etc.]

### Headers
\`\`\`
Authorization: Bearer {token}
Content-Type: application/json
\`\`\`

## Endpoints

### GET /employees
Retrieve employee information

**Parameters:**
- \`employee_id\` (optional): Specific employee ID
- \`department\` (optional): Filter by department
- \`limit\` (optional): Number of records to return

**Response:**
\`\`\`json
{
  "employees": [
    {
      "id": "12345",
      "name": "John Doe",
      "department": "IT",
      "position": "Developer"
    }
  ],
  "total": 1
}
\`\`\`

### POST /employees
Create a new employee record

**Request Body:**
\`\`\`json
{
  "name": "Jane Smith",
  "department": "HR",
  "position": "Manager",
  "start_date": "2024-01-15"
}
\`\`\`

**Response:**
\`\`\`json
{
  "id": "12346",
  "name": "Jane Smith",
  "department": "HR",
  "position": "Manager",
  "start_date": "2024-01-15",
  "created_at": "2024-01-10T10:00:00Z"
}
\`\`\`

## Data Models

### Employee
\`\`\`json
{
  "id": "string",
  "name": "string",
  "department": "string",
  "position": "string",
  "start_date": "date",
  "created_at": "datetime",
  "updated_at": "datetime"
}
\`\`\`

## Error Handling
### Error Response Format
\`\`\`json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional error details"
  }
}
\`\`\`

### Common Error Codes
- \`400\`: Bad Request
- \`401\`: Unauthorized
- \`403\`: Forbidden
- \`404\`: Not Found
- \`500\`: Internal Server Error
`
  }
]

// GET /api/documents/templates - Get all document templates
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')

    let filteredTemplates = documentTemplates

    if (type) {
      filteredTemplates = documentTemplates.filter(template => template.type === type)
    }

    return NextResponse.json({
      templates: filteredTemplates,
      total: filteredTemplates.length
    })
  } catch (error) {
    console.error('Error fetching templates:', error)
    return NextResponse.json(
      { error: 'Failed to fetch templates' },
      { status: 500 }
    )
  }
}

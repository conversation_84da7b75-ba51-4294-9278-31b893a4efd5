import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Schema for document creation
const createDocumentSchema = z.object({
  title: z.string().min(1, 'Document title is required'),
  description: z.string().optional(),
  content: z.string().optional(),
  type: z.enum(['SPECIFICATION', 'GUIDE', 'REVIEW', 'TEMPLATE', 'CODE_DOCUMENTATION', 'API_DOCUMENTATION']),
  module: z.enum(['PA', 'PT', 'PY', 'PB', 'RC', 'OM', 'PD']).optional(),
  tags: z.array(z.string()).optional().default([]),
  isPublic: z.boolean().optional().default(false),
  authorId: z.string(),
  teamId: z.string().optional(),
  taskId: z.string().optional(),
})

// GET /api/documents - Get all documents
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const type = searchParams.get('type')
    const module = searchParams.get('module')
    const authorId = searchParams.get('authorId')
    const teamId = searchParams.get('teamId')
    const search = searchParams.get('search')
    const tags = searchParams.get('tags')?.split(',').filter(Boolean)

    const skip = (page - 1) * limit

    const where: any = {}
    if (type) where.type = type
    if (module) where.module = module
    if (authorId) where.authorId = authorId
    if (teamId) where.teamId = teamId
    if (tags && tags.length > 0) {
      where.tags = {
        hasSome: tags,
      }
    }
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
      ]
    }

    const [documents, total] = await Promise.all([
      prisma.document.findMany({
        where,
        skip,
        take: limit,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          team: {
            select: {
              id: true,
              name: true,
              module: true,
            },
          },
          task: {
            select: {
              id: true,
              title: true,
              status: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      prisma.document.count({ where }),
    ])

    return NextResponse.json({
      documents,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching documents:', error)
    return NextResponse.json(
      { error: 'Failed to fetch documents' },
      { status: 500 }
    )
  }
}

// POST /api/documents - Create a new document
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = createDocumentSchema.parse(body)

    // Check if author exists
    const author = await prisma.user.findUnique({
      where: { id: validatedData.authorId },
    })

    if (!author) {
      return NextResponse.json(
        { error: 'Author not found' },
        { status: 404 }
      )
    }

    // Check if team exists (if provided)
    if (validatedData.teamId) {
      const team = await prisma.team.findUnique({
        where: { id: validatedData.teamId },
      })

      if (!team) {
        return NextResponse.json(
          { error: 'Team not found' },
          { status: 404 }
        )
      }
    }

    // Check if task exists (if provided)
    if (validatedData.taskId) {
      const task = await prisma.task.findUnique({
        where: { id: validatedData.taskId },
      })

      if (!task) {
        return NextResponse.json(
          { error: 'Task not found' },
          { status: 404 }
        )
      }
    }

    const document = await prisma.$transaction(async (tx) => {
      // Create the document
      const newDocument = await tx.document.create({
        data: {
          title: validatedData.title,
          description: validatedData.description,
          content: validatedData.content,
          type: validatedData.type,
          module: validatedData.module,
          tags: validatedData.tags,
          isPublic: validatedData.isPublic,
          authorId: validatedData.authorId,
          teamId: validatedData.teamId,
          taskId: validatedData.taskId,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          team: {
            select: {
              id: true,
              name: true,
              module: true,
            },
          },
          task: {
            select: {
              id: true,
              title: true,
              status: true,
            },
          },
        },
      })

      // Create activity log
      await tx.activity.create({
        data: {
          type: 'DOCUMENT_CREATED',
          message: `Document "${newDocument.title}" was created`,
          userId: validatedData.authorId,
          teamId: validatedData.teamId,
          module: validatedData.module || undefined,
        },
      })

      return newDocument
    })

    return NextResponse.json(document, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating document:', error)
    return NextResponse.json(
      { error: 'Failed to create document' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateDocumentSchema = z.object({
  title: z.string().min(1).optional(),
  description: z.string().optional(),
  content: z.string().optional(),
  type: z.enum(['SPECIFICATION', 'GUIDE', 'REVIEW', 'TEMPLATE', 'CODE_DOCUMENTATION', 'API_DOCUMENTATION']).optional(),
  module: z.enum(['PA', 'PT', 'PY', 'PB', 'RC', 'OM', 'PD']).optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().optional(),
  teamId: z.string().optional(),
  taskId: z.string().optional(),
})

// GET /api/documents/[id] - Get a specific document
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const document = await prisma.document.findUnique({
      where: { id: params.id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            role: true,
          },
        },
        team: {
          select: {
            id: true,
            name: true,
            module: true,
            status: true,
          },
        },
        task: {
          select: {
            id: true,
            title: true,
            status: true,
            priority: true,
          },
        },
      },
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(document)
  } catch (error) {
    console.error('Error fetching document:', error)
    return NextResponse.json(
      { error: 'Failed to fetch document' },
      { status: 500 }
    )
  }
}

// PUT /api/documents/[id] - Update a document
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const validatedData = updateDocumentSchema.parse(body)

    // Check if document exists
    const existingDocument = await prisma.document.findUnique({
      where: { id: params.id },
      include: {
        author: { select: { name: true } },
      },
    })

    if (!existingDocument) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      )
    }

    // Validate team if provided
    if (validatedData.teamId) {
      const team = await prisma.team.findUnique({
        where: { id: validatedData.teamId },
      })

      if (!team) {
        return NextResponse.json(
          { error: 'Team not found' },
          { status: 404 }
        )
      }
    }

    // Validate task if provided
    if (validatedData.taskId) {
      const task = await prisma.task.findUnique({
        where: { id: validatedData.taskId },
      })

      if (!task) {
        return NextResponse.json(
          { error: 'Task not found' },
          { status: 404 }
        )
      }
    }

    const updatedDocument = await prisma.$transaction(async (tx) => {
      // Update the document
      const document = await tx.document.update({
        where: { id: params.id },
        data: validatedData,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          team: {
            select: {
              id: true,
              name: true,
              module: true,
            },
          },
          task: {
            select: {
              id: true,
              title: true,
              status: true,
            },
          },
        },
      })

      // Create activity log
      await tx.activity.create({
        data: {
          type: 'DOCUMENT_UPDATED',
          message: `Document "${document.title}" was updated`,
          userId: existingDocument.authorId,
          teamId: document.teamId,
          module: document.module || undefined,
        },
      })

      return document
    })

    return NextResponse.json(updatedDocument)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating document:', error)
    return NextResponse.json(
      { error: 'Failed to update document' },
      { status: 500 }
    )
  }
}

// DELETE /api/documents/[id] - Delete a document
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const document = await prisma.document.findUnique({
      where: { id: params.id },
      include: {
        author: { select: { name: true } },
      },
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      )
    }

    await prisma.$transaction(async (tx) => {
      // Delete the document
      await tx.document.delete({
        where: { id: params.id },
      })

      // Create activity log
      await tx.activity.create({
        data: {
          type: 'DOCUMENT_UPDATED',
          message: `Document "${document.title}" was deleted`,
          userId: document.authorId,
          teamId: document.teamId,
          module: document.module || undefined,
        },
      })
    })

    return NextResponse.json({ message: 'Document deleted successfully' })
  } catch (error) {
    console.error('Error deleting document:', error)
    return NextResponse.json(
      { error: 'Failed to delete document' },
      { status: 500 }
    )
  }
}

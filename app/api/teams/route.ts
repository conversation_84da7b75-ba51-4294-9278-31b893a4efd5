import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Schema for team creation
const createTeamSchema = z.object({
  name: z.string().min(1, 'Team name is required'),
  description: z.string().optional(),
  module: z.enum(['PA', 'PT', 'PY', 'PB', 'RC', 'OM', 'PD']),
  deadline: z.string().datetime().optional(),
  leadId: z.string(),
  memberIds: z.array(z.string()).optional(),
})

// GET /api/teams - Get all teams
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const module = searchParams.get('module')
    const status = searchParams.get('status')

    const skip = (page - 1) * limit

    const where: any = {}
    if (module) where.module = module
    if (status) where.status = status

    const [teams, total] = await Promise.all([
      prisma.team.findMany({
        where,
        skip,
        take: limit,
        include: {
          lead: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true,
                  role: true,
                  expertise: true,
                  availability: true,
                },
              },
            },
          },
          tasks: {
            select: {
              id: true,
              title: true,
              status: true,
              priority: true,
            },
          },
          _count: {
            select: {
              members: true,
              tasks: true,
              documents: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      prisma.team.count({ where }),
    ])

    return NextResponse.json({
      teams,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching teams:', error)
    return NextResponse.json(
      { error: 'Failed to fetch teams' },
      { status: 500 }
    )
  }
}

// POST /api/teams - Create a new team
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = createTeamSchema.parse(body)

    // Check if lead exists
    const lead = await prisma.user.findUnique({
      where: { id: validatedData.leadId },
    })

    if (!lead) {
      return NextResponse.json(
        { error: 'Team lead not found' },
        { status: 404 }
      )
    }

    // Create team with transaction
    const team = await prisma.$transaction(async (tx) => {
      // Create the team
      const newTeam = await tx.team.create({
        data: {
          name: validatedData.name,
          description: validatedData.description,
          module: validatedData.module,
          deadline: validatedData.deadline ? new Date(validatedData.deadline) : null,
          leadId: validatedData.leadId,
        },
        include: {
          lead: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
        },
      })

      // Add team lead as a member with LEAD role
      await tx.teamMember.create({
        data: {
          teamId: newTeam.id,
          userId: validatedData.leadId,
          role: 'LEAD',
        },
      })

      // Add other members if provided
      if (validatedData.memberIds && validatedData.memberIds.length > 0) {
        await tx.teamMember.createMany({
          data: validatedData.memberIds.map((userId) => ({
            teamId: newTeam.id,
            userId,
            role: 'MEMBER' as const,
          })),
        })
      }

      // Create activity log
      await tx.activity.create({
        data: {
          type: 'TEAM_CREATED',
          message: `Team "${newTeam.name}" was created`,
          userId: validatedData.leadId,
          teamId: newTeam.id,
          module: validatedData.module,
        },
      })

      return newTeam
    })

    // Fetch the complete team data
    const completeTeam = await prisma.team.findUnique({
      where: { id: team.id },
      include: {
        lead: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
                role: true,
                expertise: true,
                availability: true,
              },
            },
          },
        },
        _count: {
          select: {
            members: true,
            tasks: true,
            documents: true,
          },
        },
      },
    })

    return NextResponse.json(completeTeam, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating team:', error)
    return NextResponse.json(
      { error: 'Failed to create team' },
      { status: 500 }
    )
  }
}

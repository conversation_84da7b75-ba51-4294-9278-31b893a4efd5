import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateTeamSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  module: z.enum(['PA', 'PT', 'PY', 'PB', 'RC', 'OM', 'PD']).optional(),
  deadline: z.string().datetime().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'COMPLETED', 'ARCHIVED']).optional(),
  leadId: z.string().optional(),
})

// GET /api/teams/[id] - Get a specific team
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const team = await prisma.team.findUnique({
      where: { id: params.id },
      include: {
        lead: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            role: true,
          },
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
                role: true,
                expertise: true,
                availability: true,
                currentProjects: true,
              },
            },
          },
          orderBy: {
            joinedAt: 'asc',
          },
        },
        tasks: {
          include: {
            assignee: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        documents: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        activities: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 20,
        },
        _count: {
          select: {
            members: true,
            tasks: true,
            documents: true,
          },
        },
      },
    })

    if (!team) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(team)
  } catch (error) {
    console.error('Error fetching team:', error)
    return NextResponse.json(
      { error: 'Failed to fetch team' },
      { status: 500 }
    )
  }
}

// PUT /api/teams/[id] - Update a team
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const validatedData = updateTeamSchema.parse(body)

    // Check if team exists
    const existingTeam = await prisma.team.findUnique({
      where: { id: params.id },
    })

    if (!existingTeam) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      )
    }

    // If leadId is being changed, validate the new lead
    if (validatedData.leadId) {
      const newLead = await prisma.user.findUnique({
        where: { id: validatedData.leadId },
      })

      if (!newLead) {
        return NextResponse.json(
          { error: 'New team lead not found' },
          { status: 404 }
        )
      }
    }

    const updatedTeam = await prisma.$transaction(async (tx) => {
      // Update the team
      const team = await tx.team.update({
        where: { id: params.id },
        data: {
          ...validatedData,
          deadline: validatedData.deadline ? new Date(validatedData.deadline) : undefined,
        },
        include: {
          lead: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
        },
      })

      // If lead changed, update team membership
      if (validatedData.leadId && validatedData.leadId !== existingTeam.leadId) {
        // Remove LEAD role from old lead
        await tx.teamMember.updateMany({
          where: {
            teamId: params.id,
            userId: existingTeam.leadId,
            role: 'LEAD',
          },
          data: {
            role: 'MEMBER',
          },
        })

        // Add new lead as member if not already a member
        await tx.teamMember.upsert({
          where: {
            teamId_userId: {
              teamId: params.id,
              userId: validatedData.leadId,
            },
          },
          create: {
            teamId: params.id,
            userId: validatedData.leadId,
            role: 'LEAD',
          },
          update: {
            role: 'LEAD',
          },
        })

        // Create activity log
        await tx.activity.create({
          data: {
            type: 'TEAM_UPDATED',
            message: `Team lead changed to ${team.lead.name}`,
            userId: validatedData.leadId,
            teamId: params.id,
          },
        })
      }

      return team
    })

    // Fetch complete updated team data
    const completeTeam = await prisma.team.findUnique({
      where: { id: params.id },
      include: {
        lead: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
                role: true,
                expertise: true,
                availability: true,
              },
            },
          },
        },
        _count: {
          select: {
            members: true,
            tasks: true,
            documents: true,
          },
        },
      },
    })

    return NextResponse.json(completeTeam)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating team:', error)
    return NextResponse.json(
      { error: 'Failed to update team' },
      { status: 500 }
    )
  }
}

// DELETE /api/teams/[id] - Delete a team
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const team = await prisma.team.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            tasks: true,
          },
        },
      },
    })

    if (!team) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      )
    }

    // Check if team has active tasks
    if (team._count.tasks > 0) {
      return NextResponse.json(
        { error: 'Cannot delete team with active tasks' },
        { status: 400 }
      )
    }

    await prisma.$transaction(async (tx) => {
      // Delete team members
      await tx.teamMember.deleteMany({
        where: { teamId: params.id },
      })

      // Delete activities
      await tx.activity.deleteMany({
        where: { teamId: params.id },
      })

      // Delete the team
      await tx.team.delete({
        where: { id: params.id },
      })
    })

    return NextResponse.json({ message: 'Team deleted successfully' })
  } catch (error) {
    console.error('Error deleting team:', error)
    return NextResponse.json(
      { error: 'Failed to delete team' },
      { status: 500 }
    )
  }
}

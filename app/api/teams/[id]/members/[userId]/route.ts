import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateMemberSchema = z.object({
  role: z.enum(['LEAD', 'MEMBER', 'CONTRIBUTOR']),
})

// PUT /api/teams/[id]/members/[userId] - Update member role
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; userId: string } }
) {
  try {
    const body = await request.json()
    const validatedData = updateMemberSchema.parse(body)

    // Check if team member exists
    const existingMember = await prisma.teamMember.findUnique({
      where: {
        teamId_userId: {
          teamId: params.id,
          userId: params.userId,
        },
      },
      include: {
        user: { select: { name: true } },
        team: { select: { name: true } },
      },
    })

    if (!existingMember) {
      return NextResponse.json(
        { error: 'Team member not found' },
        { status: 404 }
      )
    }

    const updatedMember = await prisma.$transaction(async (tx) => {
      // If promoting to LEAD, demote current lead
      if (validatedData.role === 'LEAD') {
        await tx.teamMember.updateMany({
          where: {
            teamId: params.id,
            role: 'LEAD',
          },
          data: {
            role: 'MEMBER',
          },
        })

        // Update team's leadId
        await tx.team.update({
          where: { id: params.id },
          data: { leadId: params.userId },
        })
      }

      // Update the member's role
      const member = await tx.teamMember.update({
        where: {
          teamId_userId: {
            teamId: params.id,
            userId: params.userId,
          },
        },
        data: { role: validatedData.role },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
              role: true,
              expertise: true,
              availability: true,
            },
          },
        },
      })

      // Create activity log
      await tx.activity.create({
        data: {
          type: 'TEAM_UPDATED',
          message: `${existingMember.user.name} role updated to ${validatedData.role}`,
          userId: params.userId,
          teamId: params.id,
        },
      })

      return member
    })

    return NextResponse.json(updatedMember)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating team member:', error)
    return NextResponse.json(
      { error: 'Failed to update team member' },
      { status: 500 }
    )
  }
}

// DELETE /api/teams/[id]/members/[userId] - Remove member from team
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; userId: string } }
) {
  try {
    // Check if team member exists
    const existingMember = await prisma.teamMember.findUnique({
      where: {
        teamId_userId: {
          teamId: params.id,
          userId: params.userId,
        },
      },
      include: {
        user: { select: { name: true } },
        team: { 
          select: { 
            name: true, 
            leadId: true,
            _count: { select: { members: true } }
          } 
        },
      },
    })

    if (!existingMember) {
      return NextResponse.json(
        { error: 'Team member not found' },
        { status: 404 }
      )
    }

    // Check if this is the team lead and there are other members
    if (existingMember.team.leadId === params.userId && existingMember.team._count.members > 1) {
      return NextResponse.json(
        { error: 'Cannot remove team lead. Transfer leadership first.' },
        { status: 400 }
      )
    }

    await prisma.$transaction(async (tx) => {
      // Remove the member
      await tx.teamMember.delete({
        where: {
          teamId_userId: {
            teamId: params.id,
            userId: params.userId,
          },
        },
      })

      // Update user's current projects count
      await tx.user.update({
        where: { id: params.userId },
        data: {
          currentProjects: {
            decrement: 1,
          },
        },
      })

      // Create activity log
      await tx.activity.create({
        data: {
          type: 'MEMBER_REMOVED',
          message: `${existingMember.user.name} left the team`,
          userId: params.userId,
          teamId: params.id,
        },
      })

      // If this was the last member, archive the team
      if (existingMember.team._count.members === 1) {
        await tx.team.update({
          where: { id: params.id },
          data: { status: 'ARCHIVED' },
        })
      }
    })

    return NextResponse.json({ message: 'Member removed successfully' })
  } catch (error) {
    console.error('Error removing team member:', error)
    return NextResponse.json(
      { error: 'Failed to remove team member' },
      { status: 500 }
    )
  }
}

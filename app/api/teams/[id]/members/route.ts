import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const addMemberSchema = z.object({
  userId: z.string(),
  role: z.enum(['LEAD', 'MEMBER', 'CONTRIBUTOR']).optional().default('MEMBER'),
})

const updateMemberSchema = z.object({
  role: z.enum(['LEAD', 'MEMBER', 'CONTRIBUTOR']),
})

// POST /api/teams/[id]/members - Add a member to the team
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const validatedData = addMemberSchema.parse(body)

    // Check if team exists
    const team = await prisma.team.findUnique({
      where: { id: params.id },
      include: {
        lead: {
          select: { name: true },
        },
      },
    })

    if (!team) {
      return NextResponse.json(
        { error: 'Team not found' },
        { status: 404 }
      )
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: validatedData.userId },
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if user is already a member
    const existingMember = await prisma.teamMember.findUnique({
      where: {
        teamId_userId: {
          teamId: params.id,
          userId: validatedData.userId,
        },
      },
    })

    if (existingMember) {
      return NextResponse.json(
        { error: 'User is already a member of this team' },
        { status: 400 }
      )
    }

    const teamMember = await prisma.$transaction(async (tx) => {
      // Add the member
      const member = await tx.teamMember.create({
        data: {
          teamId: params.id,
          userId: validatedData.userId,
          role: validatedData.role,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
              role: true,
              expertise: true,
              availability: true,
            },
          },
        },
      })

      // Update user's current projects count
      await tx.user.update({
        where: { id: validatedData.userId },
        data: {
          currentProjects: {
            increment: 1,
          },
        },
      })

      // Create activity log
      await tx.activity.create({
        data: {
          type: 'MEMBER_ADDED',
          message: `${user.name} joined the team`,
          userId: validatedData.userId,
          teamId: params.id,
        },
      })

      return member
    })

    return NextResponse.json(teamMember, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error adding team member:', error)
    return NextResponse.json(
      { error: 'Failed to add team member' },
      { status: 500 }
    )
  }
}

// GET /api/teams/[id]/members - Get all team members
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const members = await prisma.teamMember.findMany({
      where: { teamId: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            role: true,
            expertise: true,
            availability: true,
            currentProjects: true,
          },
        },
      },
      orderBy: [
        { role: 'asc' }, // LEAD first
        { joinedAt: 'asc' },
      ],
    })

    return NextResponse.json(members)
  } catch (error) {
    console.error('Error fetching team members:', error)
    return NextResponse.json(
      { error: 'Failed to fetch team members' },
      { status: 500 }
    )
  }
}

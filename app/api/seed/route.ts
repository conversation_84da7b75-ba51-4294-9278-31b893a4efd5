import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST() {
  try {
    // Create sample users
    const users = await Promise.all([
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          name: '<PERSON>',
          avatar: '/placeholder-user.jpg',
          role: 'TEAM_LEAD',
          expertise: ['ABAP', 'HCM', 'Payroll', 'Team Management'],
          availability: 'AVAILABLE',
          currentProjects: 2,
        },
      }),
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          name: '<PERSON>',
          avatar: '/placeholder-user.jpg',
          role: 'SENIOR_DEVELOPER',
          expertise: ['ABAP', 'Time Management', 'Workflow', 'Integration'],
          availability: 'AVAILABLE',
          currentProjects: 1,
        },
      }),
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          name: '<PERSON>',
          avatar: '/placeholder-user.jpg',
          role: 'DEVELOPER',
          expertise: ['ABAP', 'Benefits', 'Reports', 'Forms'],
          availability: 'BUSY',
          currentProjects: 3,
        },
      }),
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          name: 'Lisa Wilson',
          avatar: '/placeholder-user.jpg',
          role: 'DEVELOPER',
          expertise: ['ABAP', 'Personnel Administration', 'Data Migration'],
          availability: 'AVAILABLE',
          currentProjects: 1,
        },
      }),
    ])

    // Create sample teams
    const team1 = await prisma.team.upsert({
      where: { id: 'team-1' },
      update: {},
      create: {
        id: 'team-1',
        name: 'Payroll Enhancement Squad',
        description: 'Focused on improving payroll calculation logic and performance',
        module: 'PY',
        deadline: new Date('2024-03-15'),
        status: 'ACTIVE',
        leadId: users[0].id,
      },
    })

    const team2 = await prisma.team.upsert({
      where: { id: 'team-2' },
      update: {},
      create: {
        id: 'team-2',
        name: 'Time Management Innovators',
        description: 'Developing new time tracking and management features',
        module: 'PT',
        deadline: new Date('2024-02-28'),
        status: 'ACTIVE',
        leadId: users[1].id,
      },
    })

    // Add team members
    await Promise.all([
      // Team 1 members
      prisma.teamMember.upsert({
        where: {
          teamId_userId: {
            teamId: team1.id,
            userId: users[0].id,
          },
        },
        update: {},
        create: {
          teamId: team1.id,
          userId: users[0].id,
          role: 'LEAD',
        },
      }),
      prisma.teamMember.upsert({
        where: {
          teamId_userId: {
            teamId: team1.id,
            userId: users[2].id,
          },
        },
        update: {},
        create: {
          teamId: team1.id,
          userId: users[2].id,
          role: 'MEMBER',
        },
      }),
      // Team 2 members
      prisma.teamMember.upsert({
        where: {
          teamId_userId: {
            teamId: team2.id,
            userId: users[1].id,
          },
        },
        update: {},
        create: {
          teamId: team2.id,
          userId: users[1].id,
          role: 'LEAD',
        },
      }),
      prisma.teamMember.upsert({
        where: {
          teamId_userId: {
            teamId: team2.id,
            userId: users[3].id,
          },
        },
        update: {},
        create: {
          teamId: team2.id,
          userId: users[3].id,
          role: 'MEMBER',
        },
      }),
    ])

    // Create sample documents
    await Promise.all([
      prisma.document.upsert({
        where: { id: 'doc-1' },
        update: {},
        create: {
          id: 'doc-1',
          title: 'Payroll Enhancement Technical Specification',
          description: 'Comprehensive technical specification for payroll calculation improvements',
          content: '# Payroll Enhancement Technical Specification\n\n## Overview\nThis document outlines the technical requirements for enhancing the payroll calculation engine...',
          type: 'SPECIFICATION',
          module: 'PY',
          tags: ['payroll', 'calculation', 'enhancement'],
          isPublic: true,
          authorId: users[0].id,
          teamId: team1.id,
        },
      }),
      prisma.document.upsert({
        where: { id: 'doc-2' },
        update: {},
        create: {
          id: 'doc-2',
          title: 'Time Management User Guide',
          description: 'End-user guide for the new time management features',
          content: '# Time Management User Guide\n\n## Introduction\nWelcome to the enhanced time management system...',
          type: 'GUIDE',
          module: 'PT',
          tags: ['time-management', 'user-guide', 'training'],
          isPublic: true,
          authorId: users[1].id,
          teamId: team2.id,
        },
      }),
      prisma.document.upsert({
        where: { id: 'doc-3' },
        update: {},
        create: {
          id: 'doc-3',
          title: 'ABAP Code Review Checklist',
          description: 'Standard code review checklist for ABAP development',
          content: '# ABAP Code Review Checklist\n\n## General Code Quality\n- [ ] Code follows naming conventions...',
          type: 'REVIEW',
          tags: ['code-review', 'abap', 'quality'],
          isPublic: true,
          authorId: users[2].id,
        },
      }),
    ])

    return NextResponse.json({
      message: 'Seed data created successfully',
      users: users.length,
      teams: 2,
      documents: 3
    })
  } catch (error) {
    console.error('Error seeding data:', error)
    return NextResponse.json(
      { error: 'Failed to seed data', details: error },
      { status: 500 }
    )
  }
}

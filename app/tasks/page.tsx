"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Search, Filter, Plus, Calendar, User, Building, AlertCircle, CheckCircle2, Clock } from "lucide-react"
import Link from "next/link"

export default function TasksPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")

  const tasks = [
    {
      id: "HCM-001",
      title: "Payroll Schema Enhancement for Global Corp",
      description: "Implement custom payroll schema modifications for international payroll processing",
      customer: "Global Corp",
      priority: "High",
      status: "In Progress",
      module: "PA",
      assignee: "<PERSON>",
      dueDate: "2024-01-15",
      created: "2024-01-08",
      estimatedHours: 40,
      completedHours: 24,
    },
    {
      id: "HCM-002",
      title: "Time Management Customization",
      description: "Custom time recording interface for manufacturing shifts",
      customer: "Tech Solutions",
      priority: "Medium",
      status: "Code Review",
      module: "PT",
      assignee: "Sarah Johnson",
      dueDate: "2024-01-18",
      created: "2024-01-05",
      estimatedHours: 32,
      completedHours: 28,
    },
    {
      id: "HCM-003",
      title: "Benefits Administration Update",
      description: "Update benefits calculation logic for new healthcare plans",
      customer: "Healthcare Inc",
      priority: "Low",
      status: "Testing",
      module: "PB",
      assignee: "Mike Davis",
      dueDate: "2024-01-22",
      created: "2024-01-10",
      estimatedHours: 24,
      completedHours: 20,
    },
    {
      id: "HCM-004",
      title: "Recruitment Process Automation",
      description: "Automate candidate screening and interview scheduling",
      customer: "Talent Corp",
      priority: "High",
      status: "Planning",
      module: "RC",
      assignee: "Lisa Wilson",
      dueDate: "2024-01-25",
      created: "2024-01-12",
      estimatedHours: 56,
      completedHours: 8,
    },
    {
      id: "HCM-005",
      title: "Employee Self-Service Portal",
      description: "Develop mobile-friendly ESS portal for leave requests",
      customer: "Mobile First",
      priority: "Medium",
      status: "Development",
      module: "PA",
      assignee: "Tom Brown",
      dueDate: "2024-01-30",
      created: "2024-01-09",
      estimatedHours: 48,
      completedHours: 16,
    },
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Completed":
        return <CheckCircle2 className="w-4 h-4 text-green-600" />
      case "In Progress":
      case "Development":
        return <Clock className="w-4 h-4 text-blue-600" />
      case "Code Review":
      case "Testing":
        return <AlertCircle className="w-4 h-4 text-orange-600" />
      default:
        return <Clock className="w-4 h-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800"
      case "In Progress":
      case "Development":
        return "bg-blue-100 text-blue-800"
      case "Code Review":
      case "Testing":
        return "bg-orange-100 text-orange-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Task Management</h1>
          <p className="text-muted-foreground">Manage ABAP development tasks and customer requirements</p>
        </div>
        <Button asChild>
          <Link href="/tasks/new">
            <Plus className="w-4 h-4 mr-2" />
            New Task
          </Link>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 flex-wrap">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search tasks..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="planning">Planning</SelectItem>
                <SelectItem value="development">Development</SelectItem>
                <SelectItem value="code-review">Code Review</SelectItem>
                <SelectItem value="testing">Testing</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tasks Table */}
      <Card>
        <CardHeader>
          <CardTitle>Development Tasks</CardTitle>
          <CardDescription>Current ABAP development tasks and their progress</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Task</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Module</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Assignee</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Due Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tasks.map((task) => (
                <TableRow key={task.id} className="cursor-pointer hover:bg-muted/50">
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{task.id}</div>
                      <div className="text-sm font-medium">{task.title}</div>
                      <div className="text-xs text-muted-foreground">{task.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Building className="w-4 h-4 text-muted-foreground" />
                      {task.customer}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{task.module}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getPriorityColor(task.priority)}>{task.priority}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(task.status)}
                      <Badge className={getStatusColor(task.status)}>{task.status}</Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-muted-foreground" />
                      {task.assignee}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">
                        {task.completedHours}h / {task.estimatedHours}h
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${(task.completedHours / task.estimatedHours) * 100}%` }}
                        />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      {task.dueDate}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

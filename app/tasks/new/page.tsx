"use client"

import React from "react"
import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar, User, Building, AlertTriangle, Save, Eye, Plus, X, Tag, Clock, Target } from "lucide-react"
import { format } from "date-fns"

interface TaskTemplate {
  id: string
  name: string
  description: string
  type: string
  content: string
  estimatedHours: number
  module: string
}

export default function NewTaskPage() {
  const [taskTitle, setTaskTitle] = useState("")
  const [taskDescription, setTaskDescription] = useState("")
  const [customer, setCustomer] = useState("")
  const [priority, setPriority] = useState("")
  const [module, setModule] = useState("")
  const [assignee, setAssignee] = useState("")
  const [dueDate, setDueDate] = useState("")
  const [estimatedHours, setEstimatedHours] = useState("")
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState("")
  const [selectedTemplate, setSelectedTemplate] = useState("")
  const [isUrgent, setIsUrgent] = useState(false)
  const [requiresApproval, setRequiresApproval] = useState(false)

  const [templates] = useState<TaskTemplate[]>([
    {
      id: "payroll-enhancement",
      name: "Payroll Schema Enhancement",
      description: "Standard template for payroll schema modifications",
      type: "payroll",
      content: "Implement custom payroll schema modifications for international payroll processing. This includes creating new wage types, updating calculation rules, and ensuring compliance with local tax regulations.",
      estimatedHours: 40,
      module: "PA",
    },
    {
      id: "time-management",
      name: "Time Management Customization",
      description: "Template for time recording customizations",
      type: "time",
      content: "Custom time recording interface for manufacturing shifts. Develop user-friendly interfaces for time entry, approval workflows, and integration with payroll processing.",
      estimatedHours: 32,
      module: "PT",
    },
    {
      id: "benefits-update",
      name: "Benefits Administration Update",
      description: "Template for benefits calculation updates",
      type: "benefits",
      content: "Update benefits calculation logic for new healthcare plans. Modify existing calculation rules, add new benefit types, and ensure accurate deduction processing.",
      estimatedHours: 24,
      module: "PB",
    },
    {
      id: "recruitment-automation",
      name: "Recruitment Process Automation",
      description: "Template for recruitment workflow automation",
      type: "recruitment",
      content: "Automate candidate screening and interview scheduling. Create automated workflows for resume parsing, candidate scoring, and interview coordination.",
      estimatedHours: 56,
      module: "RC",
    },
  ])

  const customers = [
    { id: "global-corp", name: "Global Corp", industry: "Manufacturing" },
    { id: "tech-solutions", name: "Tech Solutions", industry: "Technology" },
    { id: "healthcare-inc", name: "Healthcare Inc", industry: "Healthcare" },
    { id: "talent-corp", name: "Talent Corp", industry: "Recruitment" },
    { id: "mobile-first", name: "Mobile First", industry: "Mobile Apps" },
  ]

  const developers = [
    { id: "john-smith", name: "John Smith", role: "Senior ABAP Developer", expertise: ["PA", "PT"] },
    { id: "sarah-johnson", name: "Sarah Johnson", role: "ABAP Developer", expertise: ["PT", "PB"] },
    { id: "mike-davis", name: "Mike Davis", role: "Technical Lead", expertise: ["PB", "RC"] },
    { id: "lisa-wilson", name: "Lisa Wilson", role: "Senior Developer", expertise: ["PA", "RC"] },
    { id: "tom-brown", name: "Tom Brown", role: "ABAP Developer", expertise: ["PA", "PT"] },
  ]

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find((t) => t.id === templateId)
    if (template) {
      setSelectedTemplate(templateId)
      setTaskDescription(template.content)
      setEstimatedHours(template.estimatedHours.toString())
      setModule(template.module)
      if (!taskTitle) {
        setTaskTitle(template.name)
      }
    }
  }

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag("")
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove))
  }

  const handleCreateTask = () => {
    // Handle task creation logic here
    const newTask = {
      id: `HCM-${Date.now().toString().slice(-6)}`,
      title: taskTitle,
      description: taskDescription,
      customer,
      priority,
      status: "Planning",
      module,
      assignee,
      dueDate,
      created: new Date().toISOString().split('T')[0],
      estimatedHours: parseInt(estimatedHours) || 0,
      completedHours: 0,
      tags,
      isUrgent,
      requiresApproval,
    }
    
    console.log("Creating new task:", newTask)
    
    // Here you would typically save to a database or API
    // For now, we'll just show a success message
    alert("Task created successfully!")
    
    // Reset form
    setTaskTitle("")
    setTaskDescription("")
    setCustomer("")
    setPriority("")
    setModule("")
    setAssignee("")
    setDueDate("")
    setEstimatedHours("")
    setTags([])
    setSelectedTemplate("")
    setIsUrgent(false)
    setRequiresApproval(false)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800 border-red-200"
      case "High":
        return "bg-orange-100 text-orange-800 border-orange-200"
      case "Medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "Low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Create New Task</h1>
          <p className="text-muted-foreground">Create a new HCM development task for customer requirements</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => window.history.back()}>
            Cancel
          </Button>
          <Button variant="outline">
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button onClick={handleCreateTask} disabled={!taskTitle || !taskDescription || !customer || !priority || !module}>
            <Save className="w-4 h-4 mr-2" />
            Create Task
          </Button>
        </div>
      </div>

      <Tabs defaultValue="create" className="space-y-6">
        <TabsList>
          <TabsTrigger value="create">Create Task</TabsTrigger>
          <TabsTrigger value="templates">Use Template</TabsTrigger>
          <TabsTrigger value="import">Import from JIRA</TabsTrigger>
        </TabsList>

        <TabsContent value="create" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-3">
            {/* Task Configuration */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Task Information
                  </CardTitle>
                  <CardDescription>Define the basic details of your task</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="task-title">Task Title *</Label>
                    <Input
                      id="task-title"
                      placeholder="e.g., Payroll Schema Enhancement for Global Corp"
                      value={taskTitle}
                      onChange={(e) => setTaskTitle(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="task-description">Description *</Label>
                    <Textarea
                      id="task-description"
                      placeholder="Detailed description of the task requirements, objectives, and deliverables..."
                      value={taskDescription}
                      onChange={(e) => setTaskDescription(e.target.value)}
                      rows={4}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="customer">Customer *</Label>
                      <Select value={customer} onValueChange={setCustomer}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select customer" />
                        </SelectTrigger>
                        <SelectContent>
                          {customers.map((customer) => (
                            <SelectItem key={customer.id} value={customer.id}>
                              <div className="flex items-center gap-2">
                                <Building className="w-4 h-4" />
                                {customer.name}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="priority">Priority *</Label>
                      <Select value={priority} onValueChange={setPriority}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Critical">
                            <div className="flex items-center gap-2">
                              <AlertTriangle className="w-4 h-4 text-red-600" />
                              Critical
                            </div>
                          </SelectItem>
                          <SelectItem value="High">
                            <div className="flex items-center gap-2">
                              <AlertTriangle className="w-4 h-4 text-orange-600" />
                              High
                            </div>
                          </SelectItem>
                          <SelectItem value="Medium">
                            <div className="flex items-center gap-2">
                              <AlertTriangle className="w-4 h-4 text-yellow-600" />
                              Medium
                            </div>
                          </SelectItem>
                          <SelectItem value="Low">
                            <div className="flex items-center gap-2">
                              <AlertTriangle className="w-4 h-4 text-green-600" />
                              Low
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="module">HCM Module *</Label>
                      <Select value={module} onValueChange={setModule}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select module" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="PA">PA - Personnel Administration</SelectItem>
                          <SelectItem value="PT">PT - Personnel Time Management</SelectItem>
                          <SelectItem value="PB">PB - Personnel Benefits</SelectItem>
                          <SelectItem value="RC">RC - Recruitment</SelectItem>
                          <SelectItem value="PY">PY - Payroll</SelectItem>
                          <SelectItem value="OM">OM - Organizational Management</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="assignee">Assignee</Label>
                      <Select value={assignee} onValueChange={setAssignee}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select assignee" />
                        </SelectTrigger>
                        <SelectContent>
                          {developers.map((dev) => (
                            <SelectItem key={dev.id} value={dev.id}>
                              <div className="flex items-center gap-2">
                                <User className="w-4 h-4" />
                                {dev.name} - {dev.role}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="due-date">Due Date</Label>
                      <Input
                        id="due-date"
                        type="date"
                        value={dueDate}
                        onChange={(e) => setDueDate(e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="estimated-hours">Estimated Hours</Label>
                      <Input
                        id="estimated-hours"
                        type="number"
                        placeholder="e.g., 40"
                        value={estimatedHours}
                        onChange={(e) => setEstimatedHours(e.target.value)}
                      />
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="space-y-2">
                    <Label>Tags</Label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add a tag..."
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && handleAddTag()}
                      />
                      <Button type="button" onClick={handleAddTag}>
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    {tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                            <Tag className="w-3 h-3" />
                            {tag}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveTag(tag)}
                              className="h-4 w-4 p-0 ml-1"
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Additional Requirements */}
              <Card>
                <CardHeader>
                  <CardTitle>Additional Requirements</CardTitle>
                  <CardDescription>Specify any additional requirements or constraints</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="urgent" checked={isUrgent} onCheckedChange={(checked) => setIsUrgent(checked === true)} />
                    <Label htmlFor="urgent">Mark as urgent</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="approval" checked={requiresApproval} onCheckedChange={(checked) => setRequiresApproval(checked === true)} />
                    <Label htmlFor="approval">Requires management approval</Label>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Task Settings */}
            <div className="space-y-6">
              {/* Task Summary */}
              <Card>
                <CardHeader>
                  <CardTitle>Task Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Customer:</span>
                    <span className="font-medium">
                      {customer ? customers.find(c => c.id === customer)?.name : "Not selected"}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>HCM Module:</span>
                    <span className="font-medium">{module || "Not selected"}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Priority:</span>
                    <span className="font-medium">
                      {priority && (
                        <Badge className={getPriorityColor(priority)}>
                          {priority}
                        </Badge>
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Assignee:</span>
                    <span className="font-medium">
                      {assignee ? developers.find(d => d.id === assignee)?.name : "Unassigned"}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Due Date:</span>
                    <span className="font-medium">{dueDate || "Not set"}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Estimated Hours:</span>
                    <span className="font-medium">{estimatedHours || "Not set"}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule Meeting
                  </Button>
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <User className="w-4 h-4 mr-2" />
                    Assign to Team
                  </Button>
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <Clock className="w-4 h-4 mr-2" />
                    Set Reminders
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Task Templates</CardTitle>
              <CardDescription>Choose from predefined task templates to get started quickly</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {templates.map((template) => (
                  <Card
                    key={template.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedTemplate === template.id ? "ring-2 ring-primary" : ""
                    }`}
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <CardHeader>
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <CardDescription>{template.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm">
                          <Badge variant="outline">{template.module}</Badge>
                          <span className="text-muted-foreground">•</span>
                          <span className="text-muted-foreground">{template.estimatedHours} hours</span>
                        </div>
                        <p className="text-sm text-muted-foreground line-clamp-3">{template.content}</p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="import" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Import from JIRA</CardTitle>
              <CardDescription>Import task details from JIRA tickets</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="jira-url">JIRA URL</Label>
                <Input id="jira-url" placeholder="https://your-company.atlassian.net/browse/PROJ-123" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="jira-token">API Token</Label>
                <Input id="jira-token" type="password" placeholder="Enter your JIRA API token" />
              </div>
              <Button className="w-full">Import Task</Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 
"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Bot, 
  Send, 
  Code, 
  Zap, 
  Search, 
  BookOpen, 
  MessageSquare, 
  Copy, 
  ThumbsUp, 
  ThumbsDown,
  Sparkles,
  Brain,
  FileCode,
  CheckCircle
} from "lucide-react"

interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  type?: string
  suggestions?: string[]
}

interface AIResponse {
  code?: string
  explanation?: string
  suggestions?: string[]
  analysis?: any
  issues?: any[]
  recommendations?: string[]
}

export default function AIAssistantPage() {
  const [activeTab, setActiveTab] = useState("chat")
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [currentMessage, setCurrentMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [conversationId, setConversationId] = useState<string>()
  
  // Code generation states
  const [codePrompt, setCodePrompt] = useState("")
  const [codeType, setCodeType] = useState("")
  const [hcmModule, setHcmModule] = useState("")
  const [generatedCode, setGeneratedCode] = useState("")
  const [codeExplanation, setCodeExplanation] = useState("")
  
  // Code analysis states
  const [codeToAnalyze, setCodeToAnalyze] = useState("")
  const [analysisResult, setAnalysisResult] = useState<any>(null)
  
  const chatEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Add welcome message
    setChatMessages([{
      role: 'assistant',
      content: "Hello! I'm your ABAP AI assistant. I can help you with code generation, optimization, HCM guidance, and best practices. How can I assist you today?",
      timestamp: new Date().toISOString(),
      type: 'greeting',
      suggestions: [
        'Generate a simple ABAP report',
        'Explain HCM infotypes',
        'Review my ABAP code',
        'Show best practices'
      ]
    }])
  }, [])

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [chatMessages])

  const sendChatMessage = async () => {
    if (!currentMessage.trim() || isLoading) return

    const userMessage: ChatMessage = {
      role: 'user',
      content: currentMessage,
      timestamp: new Date().toISOString()
    }

    setChatMessages(prev => [...prev, userMessage])
    setCurrentMessage("")
    setIsLoading(true)

    try {
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: currentMessage,
          conversationId,
          context: { module: hcmModule }
        })
      })

      if (response.ok) {
        const data = await response.json()
        setConversationId(data.conversationId)
        
        const assistantMessage: ChatMessage = {
          role: 'assistant',
          content: data.response.content,
          timestamp: data.timestamp,
          type: data.response.type,
          suggestions: data.response.suggestions
        }
        
        setChatMessages(prev => [...prev, assistantMessage])
      } else {
        throw new Error('Failed to get AI response')
      }
    } catch (error) {
      console.error('Error sending message:', error)
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: "I'm sorry, I encountered an error. Please try again.",
        timestamp: new Date().toISOString()
      }
      setChatMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const generateCode = async () => {
    if (!codePrompt.trim() || isLoading) return

    setIsLoading(true)
    try {
      const response = await fetch('/api/ai/abap', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'generate',
          prompt: codePrompt,
          context: {
            module: hcmModule,
            objectType: codeType
          }
        })
      })

      if (response.ok) {
        const data = await response.json()
        setGeneratedCode(data.response.code || '')
        setCodeExplanation(data.response.explanation || '')
      } else {
        throw new Error('Failed to generate code')
      }
    } catch (error) {
      console.error('Error generating code:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const analyzeCode = async () => {
    if (!codeToAnalyze.trim() || isLoading) return

    setIsLoading(true)
    try {
      const response = await fetch('/api/ai/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          code: codeToAnalyze,
          analysisType: 'comprehensive',
          context: { module: hcmModule }
        })
      })

      if (response.ok) {
        const data = await response.json()
        setAnalysisResult(data.analysis)
      } else {
        throw new Error('Failed to analyze code')
      }
    } catch (error) {
      console.error('Error analyzing code:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const handleSuggestionClick = (suggestion: string) => {
    setCurrentMessage(suggestion)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Brain className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">AI ABAP Assistant</h1>
            <p className="text-muted-foreground">Your intelligent companion for ABAP development and HCM expertise</p>
          </div>
        </div>
        <Badge variant="secondary" className="flex items-center gap-2">
          <Sparkles className="w-4 h-4" />
          AI-Powered
        </Badge>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="w-4 h-4" />
            Chat Assistant
          </TabsTrigger>
          <TabsTrigger value="generate" className="flex items-center gap-2">
            <Code className="w-4 h-4" />
            Code Generation
          </TabsTrigger>
          <TabsTrigger value="analyze" className="flex items-center gap-2">
            <Search className="w-4 h-4" />
            Code Analysis
          </TabsTrigger>
          <TabsTrigger value="learn" className="flex items-center gap-2">
            <BookOpen className="w-4 h-4" />
            Learning Hub
          </TabsTrigger>
        </TabsList>

        {/* Chat Assistant Tab */}
        <TabsContent value="chat" className="space-y-4">
          <Card className="h-[600px] flex flex-col">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bot className="w-5 h-5" />
                ABAP Chat Assistant
              </CardTitle>
              <CardDescription>
                Ask questions, get code help, and learn ABAP best practices
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              {/* Chat Messages */}
              <div className="flex-1 overflow-y-auto space-y-4 mb-4">
                {chatMessages.map((message, index) => (
                  <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] rounded-lg p-3 ${
                      message.role === 'user' 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-muted'
                    }`}>
                      <div className="whitespace-pre-wrap">{message.content}</div>
                      {message.suggestions && (
                        <div className="mt-3 flex flex-wrap gap-2">
                          {message.suggestions.map((suggestion, idx) => (
                            <Button
                              key={idx}
                              variant="outline"
                              size="sm"
                              onClick={() => handleSuggestionClick(suggestion)}
                              className="text-xs"
                            >
                              {suggestion}
                            </Button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-muted rounded-lg p-3">
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                        <span>AI is thinking...</span>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={chatEndRef} />
              </div>

              {/* Chat Input */}
              <div className="flex gap-2">
                <Input
                  value={currentMessage}
                  onChange={(e) => setCurrentMessage(e.target.value)}
                  placeholder="Ask me anything about ABAP or HCM..."
                  onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}
                  disabled={isLoading}
                />
                <Button onClick={sendChatMessage} disabled={isLoading || !currentMessage.trim()}>
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Code Generation Tab */}
        <TabsContent value="generate" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileCode className="w-5 h-5" />
                  Code Generation
                </CardTitle>
                <CardDescription>
                  Describe what you want to build and get ABAP code
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">What do you want to create?</label>
                  <Textarea
                    value={codePrompt}
                    onChange={(e) => setCodePrompt(e.target.value)}
                    placeholder="e.g., Create a report to display employee data from PA0001..."
                    rows={3}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Object Type</label>
                    <Select value={codeType} onValueChange={setCodeType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="report">Report</SelectItem>
                        <SelectItem value="class">Class</SelectItem>
                        <SelectItem value="function">Function Module</SelectItem>
                        <SelectItem value="interface">Interface</SelectItem>
                        <SelectItem value="program">Program</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">HCM Module</label>
                    <Select value={hcmModule} onValueChange={setHcmModule}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select module" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PA">Personnel Administration</SelectItem>
                        <SelectItem value="PT">Time Management</SelectItem>
                        <SelectItem value="PY">Payroll</SelectItem>
                        <SelectItem value="PB">Benefits</SelectItem>
                        <SelectItem value="RC">Recruitment</SelectItem>
                        <SelectItem value="OM">Organizational Management</SelectItem>
                        <SelectItem value="PD">Personnel Development</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <Button onClick={generateCode} disabled={isLoading || !codePrompt.trim()} className="w-full">
                  <Zap className="w-4 h-4 mr-2" />
                  {isLoading ? 'Generating...' : 'Generate Code'}
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Generated Code</CardTitle>
                {generatedCode && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(generatedCode)}
                    className="w-fit"
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Code
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                {generatedCode ? (
                  <div className="space-y-4">
                    <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm">
                      <code>{generatedCode}</code>
                    </pre>
                    {codeExplanation && (
                      <div className="p-4 bg-blue-50 rounded-lg">
                        <h4 className="font-medium mb-2">Explanation:</h4>
                        <p className="text-sm text-muted-foreground">{codeExplanation}</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    Generated code will appear here
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Code Analysis Tab */}
        <TabsContent value="analyze" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="w-5 h-5" />
                  Code Analysis
                </CardTitle>
                <CardDescription>
                  Paste your ABAP code for performance and quality analysis
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">ABAP Code</label>
                  <Textarea
                    value={codeToAnalyze}
                    onChange={(e) => setCodeToAnalyze(e.target.value)}
                    placeholder="Paste your ABAP code here..."
                    rows={12}
                    className="font-mono text-sm"
                  />
                </div>
                
                <Button onClick={analyzeCode} disabled={isLoading || !codeToAnalyze.trim()} className="w-full">
                  <Search className="w-4 h-4 mr-2" />
                  {isLoading ? 'Analyzing...' : 'Analyze Code'}
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Analysis Results</CardTitle>
              </CardHeader>
              <CardContent>
                {analysisResult ? (
                  <div className="space-y-4">
                    {/* Quality Score */}
                    <div className="p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">Code Quality Score</h4>
                        <Badge variant={analysisResult.qualityScore >= 80 ? 'default' : analysisResult.qualityScore >= 60 ? 'secondary' : 'destructive'}>
                          {analysisResult.qualityScore}/100
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{analysisResult.summary}</p>
                    </div>

                    {/* Metrics */}
                    {analysisResult.metrics && (
                      <div>
                        <h4 className="font-medium mb-2">Code Metrics</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex justify-between">
                            <span>Lines of Code:</span>
                            <span className="font-medium">{analysisResult.metrics.codeLines}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Complexity:</span>
                            <span className="font-medium">{analysisResult.metrics.complexity}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Comment Ratio:</span>
                            <span className="font-medium">{(analysisResult.metrics.commentRatio * 100).toFixed(1)}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Maintainability:</span>
                            <span className="font-medium">{analysisResult.metrics.maintainabilityIndex.toFixed(1)}</span>
                          </div>
                        </div>
                      </div>
                    )}

                    {analysisResult.issues && analysisResult.issues.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Issues Found ({analysisResult.issues.length})</h4>
                        <div className="space-y-2 max-h-64 overflow-y-auto">
                          {analysisResult.issues.map((issue: any, index: number) => (
                            <div key={index} className="p-3 border rounded-lg">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge variant={
                                  issue.severity === 'Critical' ? 'destructive' :
                                  issue.severity === 'High' ? 'destructive' :
                                  issue.severity === 'Medium' ? 'secondary' : 'outline'
                                }>
                                  {issue.severity}
                                </Badge>
                                <span className="font-medium text-sm">{issue.type}</span>
                                {issue.line && <span className="text-xs text-muted-foreground">Line {issue.line}</span>}
                              </div>
                              <p className="text-sm text-muted-foreground mb-2">{issue.message}</p>
                              <p className="text-sm text-green-600">{issue.suggestion}</p>
                              {issue.code && (
                                <code className="text-xs bg-muted p-1 rounded mt-2 block">{issue.code}</code>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {analysisResult.recommendations && (
                      <div>
                        <h4 className="font-medium mb-2">Recommendations</h4>
                        <ul className="space-y-1">
                          {analysisResult.recommendations.map((rec: string, index: number) => (
                            <li key={index} className="flex items-start gap-2 text-sm">
                              <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                              {rec}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    Analysis results will appear here
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Learning Hub Tab */}
        <TabsContent value="learn" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">ABAP Fundamentals</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Data types and variables</li>
                  <li>• Internal tables</li>
                  <li>• SELECT statements</li>
                  <li>• Control structures</li>
                  <li>• Modularization</li>
                </ul>
                <Button variant="outline" className="w-full mt-4">
                  <BookOpen className="w-4 h-4 mr-2" />
                  Learn More
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">HCM Modules</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Personnel Administration (PA)</li>
                  <li>• Time Management (PT)</li>
                  <li>• Payroll (PY)</li>
                  <li>• Benefits (PB)</li>
                  <li>• Recruitment (RC)</li>
                </ul>
                <Button variant="outline" className="w-full mt-4">
                  <BookOpen className="w-4 h-4 mr-2" />
                  Explore Modules
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Best Practices</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Performance optimization</li>
                  <li>• Security guidelines</li>
                  <li>• Error handling</li>
                  <li>• Code documentation</li>
                  <li>• Testing strategies</li>
                </ul>
                <Button variant="outline" className="w-full mt-4">
                  <BookOpen className="w-4 h-4 mr-2" />
                  View Guidelines
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

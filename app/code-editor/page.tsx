"use client"

import { useState, useRef } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  Play,
  Save,
  Download,
  Upload,
  Search,
  Settings,
  AlertTriangle,
  CheckCircle2,
  Info,
  Zap,
  FileText,
  GitBranch,
  Eye,
  Code2,
} from "lucide-react"

interface TestResults {
  passed: number
  failed: number
  coverage: number
  duration: string
}

export default function CodeEditorPage() {
  const [code, setCode] = useState(`REPORT zhcm_payroll_calc.

DATA: lv_pernr TYPE persno,
      lv_amount TYPE p DECIMALS 2,
      lt_payroll TYPE TABLE OF pc207.

PARAMETERS: p_pernr TYPE persno OBLIGATORY.

START-OF-SELECTION.
  lv_pernr = p_pernr.
  
  " Get payroll data
  SELECT * FROM pc207
    INTO TABLE lt_payroll
    WHERE pernr = lv_pernr.
    
  " Calculate total amount
  LOOP AT lt_payroll INTO DATA(ls_payroll).
    lv_amount = lv_amount + ls_payroll-betrg.
  ENDLOOP.
  
  WRITE: / 'Employee:', lv_pernr,
         / 'Total Amount:', lv_amount.`)

  const [analysisResults, setAnalysisResults] = useState([
    {
      type: "warning",
      line: 12,
      column: 5,
      message: "Consider using newer ABAP syntax with VALUE operator",
      severity: "Medium",
      suggestion: "Replace LOOP with VALUE or REDUCE for better performance",
    },
    {
      type: "info",
      line: 8,
      column: 1,
      message: "Add error handling for database operations",
      severity: "Low",
      suggestion: "Add TRY-CATCH block around SELECT statement",
    },
  ])

  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [testResults, setTestResults] = useState<TestResults | null>(null)

  const editorRef = useRef<HTMLTextAreaElement>(null)

  const runAnalysis = () => {
    setIsAnalyzing(true)
    setTimeout(() => {
      setIsAnalyzing(false)
      // Simulate analysis results
    }, 2000)
  }

  const runTests = () => {
    setIsTesting(true)
    setTimeout(() => {
      setIsTesting(false)
      setTestResults({
        passed: 8,
        failed: 1,
        coverage: 85,
        duration: "2.3s",
      })
    }, 3000)
  }

  const getIssueIcon = (type: string) => {
    switch (type) {
      case "error":
        return <AlertTriangle className="w-4 h-4 text-red-600" />
      case "warning":
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />
      case "info":
        return <Info className="w-4 h-4 text-blue-600" />
      default:
        return <Info className="w-4 h-4" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "High":
        return "bg-red-100 text-red-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">ABAP Code Editor</h1>
          <p className="text-muted-foreground">Advanced code editor with real-time analysis and testing</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={runAnalysis} disabled={isAnalyzing}>
            {isAnalyzing ? <Zap className="w-4 h-4 mr-2 animate-spin" /> : <Search className="w-4 h-4 mr-2" />}
            {isAnalyzing ? "Analyzing..." : "Analyze"}
          </Button>
          <Button variant="outline" onClick={runTests} disabled={isTesting}>
            {isTesting ? <Zap className="w-4 h-4 mr-2 animate-spin" /> : <Play className="w-4 h-4 mr-2" />}
            {isTesting ? "Testing..." : "Run Tests"}
          </Button>
          <Button>
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
        </div>
      </div>

      {/* File Management */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <Select defaultValue="zhcm_payroll_calc">
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select file" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="zhcm_payroll_calc">ZHCM_PAYROLL_CALC</SelectItem>
                <SelectItem value="zhcm_time_mgmt">ZHCM_TIME_MGMT</SelectItem>
                <SelectItem value="zhcm_benefits">ZHCM_BENEFITS</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Upload className="w-3 h-3 mr-1" />
                Upload
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-3 h-3 mr-1" />
                Download
              </Button>
              <Button variant="outline" size="sm">
                <GitBranch className="w-3 h-3 mr-1" />
                Commit
              </Button>
            </div>
            <div className="flex-1" />
            <Badge className="bg-green-100 text-green-800">Saved</Badge>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Code Editor */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code2 className="w-5 h-5" />
              Code Editor
            </CardTitle>
            <CardDescription>ABAP code editor with syntax highlighting and IntelliSense</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Editor Toolbar */}
              <div className="flex items-center justify-between border-b pb-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">ABAP</Badge>
                  <Badge variant="outline">Line 1, Col 1</Badge>
                </div>
                <div className="flex gap-1">
                  <Button variant="ghost" size="sm">
                    <Search className="w-3 h-3" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Settings className="w-3 h-3" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Eye className="w-3 h-3" />
                  </Button>
                </div>
              </div>

              {/* Code Editor Area */}
              <div className="relative">
                <div className="absolute left-0 top-0 w-12 bg-muted/30 h-full flex flex-col text-xs text-muted-foreground">
                  {code.split("\n").map((_, index) => (
                    <div key={index} className="h-6 flex items-center justify-center border-r">
                      {index + 1}
                    </div>
                  ))}
                </div>
                <Textarea
                  ref={editorRef}
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  className="font-mono text-sm min-h-[400px] pl-14 resize-none"
                  placeholder="Enter your ABAP code here..."
                />
              </div>

              {/* Editor Status */}
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center gap-4">
                  <span>Lines: {code.split("\n").length}</span>
                  <span>Characters: {code.length}</span>
                  <span>Encoding: UTF-8</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span>Connected to SAP DEV</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Analysis Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5" />
              Code Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="issues" className="space-y-4">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="issues">Issues</TabsTrigger>
                <TabsTrigger value="tests">Tests</TabsTrigger>
                <TabsTrigger value="metrics">Metrics</TabsTrigger>
              </TabsList>

              <TabsContent value="issues" className="space-y-3">
                {analysisResults.map((issue, index) => (
                  <div key={index} className="border rounded-lg p-3 space-y-2">
                    <div className="flex items-center gap-2">
                      {getIssueIcon(issue.type)}
                      <Badge className={getSeverityColor(issue.severity)} variant="outline">
                        {issue.severity}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        Line {issue.line}:{issue.column}
                      </span>
                    </div>
                    <p className="text-sm">{issue.message}</p>
                    <div className="bg-muted p-2 rounded text-xs">
                      <strong>Suggestion:</strong> {issue.suggestion}
                    </div>
                  </div>
                ))}
              </TabsContent>

              <TabsContent value="tests" className="space-y-3">
                {testResults ? (
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex justify-between">
                        <span>Passed:</span>
                        <Badge className="bg-green-100 text-green-800">{testResults.passed}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Failed:</span>
                        <Badge className="bg-red-100 text-red-800">{testResults.failed}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Coverage:</span>
                        <Badge variant="outline">{testResults.coverage}%</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Duration:</span>
                        <Badge variant="outline">{testResults.duration}</Badge>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle2 className="w-4 h-4 text-green-600" />
                        <span>Payroll calculation test</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <AlertTriangle className="w-4 h-4 text-red-600" />
                        <span>Error handling test</span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-4 text-muted-foreground">
                    <Play className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>Run tests to see results</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="metrics" className="space-y-3">
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Complexity:</span>
                    <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Maintainability:</span>
                    <Badge className="bg-green-100 text-green-800">Good</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Performance:</span>
                    <Badge className="bg-blue-100 text-blue-800">Optimized</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Security:</span>
                    <Badge className="bg-green-100 text-green-800">Secure</Badge>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common development tasks and shortcuts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-4">
            <Button variant="outline" className="justify-start bg-transparent">
              <FileText className="w-4 h-4 mr-2" />
              Generate Documentation
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <GitBranch className="w-4 h-4 mr-2" />
              Create Branch
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Zap className="w-4 h-4 mr-2" />
              Deploy to QAS
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Search className="w-4 h-4 mr-2" />
              Code Review
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

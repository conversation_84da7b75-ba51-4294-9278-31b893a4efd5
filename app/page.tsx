"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  Activity,
  FileText,
  GitBranch,
  Database,
  Users,
  TrendingUp,
  Target,
  Plus,
  BookOpen,
  UserPlus,
} from "lucide-react"
import Link from "next/link"
import { TeamOverview } from "@/components/team-overview"
import { DocumentLibrary } from "@/components/document-library"
import { HCMIntegrationStatus } from "@/components/hcm-integration-status"

export default function Dashboard() {
  const [realtimeData, setRealtimeData] = useState({
    activeTasks: 28,
    activeTeams: 6,
    documentsCreated: 15,
    hcmConnections: 3,
    codeCommits: 42,
    completedToday: 12,
  })

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setRealtimeData((prev) => ({
        ...prev,
        activeTasks: prev.activeTasks + Math.floor(Math.random() * 3) - 1,
        codeCommits: prev.codeCommits + Math.floor(Math.random() * 2),
        completedToday: prev.completedToday + Math.floor(Math.random() * 2),
      }))
    }, 8000)

    return () => clearInterval(interval)
  }, [])

  const stats = [
    {
      title: "Active HCM Tasks",
      value: realtimeData.activeTasks.toString(),
      change: "+15%",
      icon: Activity,
      color: "text-blue-600",
      description: "Payroll, Time Mgmt, Benefits",
    },
    {
      title: "Development Teams",
      value: realtimeData.activeTeams.toString(),
      change: "+2",
      icon: Users,
      color: "text-green-600",
      description: "Collaborative workgroups",
    },
    {
      title: "Documents Created",
      value: realtimeData.documentsCreated.toString(),
      change: "+8",
      icon: FileText,
      color: "text-purple-600",
      description: "Specs, guides, reviews",
    },
    {
      title: "HCM Integrations",
      value: realtimeData.hcmConnections.toString(),
      change: "100%",
      icon: Database,
      color: "text-orange-600",
      description: "DEV, QAS, PRD systems",
    },
  ]

  const recentActivities = [
    {
      type: "team",
      message: "New team 'Payroll Enhancement' created with 4 members",
      user: "Sarah Johnson",
      time: "5 minutes ago",
      module: "PY",
    },
    {
      type: "document",
      message: "Technical specification for Time Management API completed",
      user: "John Smith",
      time: "12 minutes ago",
      module: "PT",
    },
    {
      type: "commit",
      message: "Benefits calculation logic updated in branch feature/benefits-v2",
      user: "Mike Davis",
      time: "25 minutes ago",
      module: "PB",
    },
    {
      type: "integration",
      message: "HCM Production system health check completed successfully",
      user: "System",
      time: "1 hour ago",
      module: "SYS",
    },
  ]

  const quickActions = [
    {
      title: "Create New Task",
      description: "Start a new HCM development task",
      icon: Plus,
      href: "/tasks/new",
      color: "bg-blue-50 hover:bg-blue-100 border-blue-200",
    },
    {
      title: "Form Team",
      description: "Create collaborative development team",
      icon: UserPlus,
      href: "/teams/new",
      color: "bg-green-50 hover:bg-green-100 border-green-200",
    },
    {
      title: "New Document",
      description: "Create technical documentation",
      icon: BookOpen,
      href: "/documents/new",
      color: "bg-purple-50 hover:bg-purple-100 border-purple-200",
    },
    {
      title: "Code Repository",
      description: "Manage version control",
      icon: GitBranch,
      href: "/version-control",
      color: "bg-orange-50 hover:bg-orange-100 border-orange-200",
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">SAP HCM Development Hub</h1>
          <p className="text-muted-foreground">Collaborative ABAP development platform for Human Capital Management</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-sm text-muted-foreground">All Systems Online</span>
          </div>
          <Button asChild>
            <Link href="/tasks/new">
              <Plus className="w-4 h-4 mr-2" />
              New Task
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <Card key={index} className="relative overflow-hidden hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                <TrendingUp className="h-3 w-3 text-green-600" />
                <span className="text-green-600">{stat.change}</span>
                <span>this week</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">{stat.description}</p>
            </CardContent>
            <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-primary/20 to-transparent" />
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Start your HCM development workflow</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {quickActions.map((action, index) => (
              <Link key={index} href={action.href}>
                <Card className={`h-full cursor-pointer transition-all hover:shadow-md ${action.color}`}>
                  <CardContent className="p-4 flex flex-col items-center text-center space-y-2">
                    <action.icon className="w-8 h-8" />
                    <h3 className="font-semibold">{action.title}</h3>
                    <p className="text-sm text-muted-foreground">{action.description}</p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="teams">Teams</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="integration">HCM Status</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* HCM Module Progress */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  HCM Module Progress
                </CardTitle>
                <CardDescription>Development progress across HCM modules</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  { module: "Personnel Administration (PA)", progress: 85, tasks: 12, color: "bg-blue-500" },
                  { module: "Time Management (PT)", progress: 72, tasks: 8, color: "bg-green-500" },
                  { module: "Payroll (PY)", progress: 91, tasks: 15, color: "bg-purple-500" },
                  { module: "Benefits (PB)", progress: 68, tasks: 6, color: "bg-orange-500" },
                  { module: "Recruitment (RC)", progress: 45, tasks: 4, color: "bg-red-500" },
                ].map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">{item.module}</span>
                      <span className="text-muted-foreground">{item.tasks} tasks</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Progress value={item.progress} className="flex-1 h-2" />
                      <span className="text-sm font-medium w-12">{item.progress}%</span>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Recent Activity
                </CardTitle>
                <CardDescription>Latest development activities</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                      <div
                        className={`w-2 h-2 rounded-full mt-2 ${
                          activity.type === "team"
                            ? "bg-green-500"
                            : activity.type === "document"
                              ? "bg-purple-500"
                              : activity.type === "commit"
                                ? "bg-blue-500"
                                : "bg-orange-500"
                        }`}
                      />
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.message}</p>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                          <span>{activity.user}</span>
                          <span>•</span>
                          <Badge variant="outline" className="text-xs">
                            {activity.module}
                          </Badge>
                          <span>•</span>
                          <span>{activity.time}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="teams">
          <TeamOverview />
        </TabsContent>

        <TabsContent value="documents">
          <DocumentLibrary />
        </TabsContent>

        <TabsContent value="integration">
          <HCMIntegrationStatus />
        </TabsContent>

        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle>Development Activity Timeline</CardTitle>
              <CardDescription>Comprehensive activity log across all projects</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities
                  .concat([
                    {
                      type: "commit",
                      message: "Merged feature branch for employee data validation",
                      user: "Lisa Wilson",
                      time: "2 hours ago",
                      module: "PA",
                    },
                    {
                      type: "document",
                      message: "Code review checklist template created",
                      user: "Tom Brown",
                      time: "3 hours ago",
                      module: "DOC",
                    },
                  ])
                  .map((activity, index) => (
                    <div key={index} className="flex items-start gap-4 p-4 border rounded-lg hover:bg-muted/50">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted">
                        {activity.type === "team" ? (
                          <Users className="w-4 h-4" />
                        ) : activity.type === "document" ? (
                          <FileText className="w-4 h-4" />
                        ) : activity.type === "commit" ? (
                          <GitBranch className="w-4 h-4" />
                        ) : (
                          <Database className="w-4 h-4" />
                        )}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{activity.message}</p>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                          <span>{activity.user}</span>
                          <span>•</span>
                          <Badge variant="outline">{activity.module}</Badge>
                          <span>•</span>
                          <span>{activity.time}</span>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

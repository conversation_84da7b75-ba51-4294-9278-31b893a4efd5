"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter, Plus, Users, Calendar, Target, Clock } from "lucide-react"
import Link from "next/link"

interface Team {
  id: string
  name: string
  description?: string
  module: string
  deadline?: string
  status: string
  createdAt: string
  lead: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  members: Array<{
    user: {
      id: string
      name: string
      email: string
      avatar?: string
      role: string
    }
  }>
  _count: {
    members: number
    tasks: number
    documents: number
  }
}

export default function TeamsPage() {
  const [teams, setTeams] = useState<Team[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [moduleFilter, setModuleFilter] = useState("")
  const [statusFilter, setStatusFilter] = useState("")

  useEffect(() => {
    const fetchTeams = async () => {
      try {
        const params = new URLSearchParams()
        if (moduleFilter) params.append('module', moduleFilter)
        if (statusFilter) params.append('status', statusFilter)
        
        const response = await fetch(`/api/teams?${params.toString()}`)
        if (response.ok) {
          const data = await response.json()
          setTeams(data.teams || [])
        } else {
          console.error('Failed to fetch teams')
          setTeams([])
        }
      } catch (error) {
        console.error('Error fetching teams:', error)
        setTeams([])
      } finally {
        setLoading(false)
      }
    }

    fetchTeams()
  }, [moduleFilter, statusFilter])

  const filteredTeams = teams.filter(team =>
    team.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    team.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    team.lead.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800'
      case 'INACTIVE': return 'bg-gray-100 text-gray-800'
      case 'COMPLETED': return 'bg-blue-100 text-blue-800'
      case 'ARCHIVED': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getModuleColor = (module: string) => {
    const colors: Record<string, string> = {
      PA: 'bg-blue-100 text-blue-800',
      PT: 'bg-green-100 text-green-800',
      PY: 'bg-purple-100 text-purple-800',
      PB: 'bg-orange-100 text-orange-800',
      RC: 'bg-red-100 text-red-800',
      OM: 'bg-indigo-100 text-indigo-800',
      PD: 'bg-pink-100 text-pink-800',
    }
    return colors[module] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Teams</h1>
          <p className="text-muted-foreground">Manage development teams and collaboration</p>
        </div>
        <Button asChild>
          <Link href="/teams/new">
            <Plus className="w-4 h-4 mr-2" />
            Create Team
          </Link>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search teams..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={moduleFilter} onValueChange={setModuleFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Modules" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Modules</SelectItem>
                <SelectItem value="PA">Personnel Administration</SelectItem>
                <SelectItem value="PT">Time Management</SelectItem>
                <SelectItem value="PY">Payroll</SelectItem>
                <SelectItem value="PB">Benefits</SelectItem>
                <SelectItem value="RC">Recruitment</SelectItem>
                <SelectItem value="OM">Organizational Management</SelectItem>
                <SelectItem value="PD">Personnel Development</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Statuses</SelectItem>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="INACTIVE">Inactive</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="ARCHIVED">Archived</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Teams Grid */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-muted-foreground">Loading teams...</div>
        </div>
      ) : filteredTeams.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Users className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No teams found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {teams.length === 0 
                ? "Get started by creating your first development team"
                : "Try adjusting your search or filter criteria"
              }
            </p>
            {teams.length === 0 && (
              <Button asChild>
                <Link href="/teams/new">
                  <Plus className="w-4 h-4 mr-2" />
                  Create First Team
                </Link>
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredTeams.map((team) => (
            <Card key={team.id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{team.name}</CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge className={getModuleColor(team.module)}>
                        {team.module}
                      </Badge>
                      <Badge className={getStatusColor(team.status)}>
                        {team.status}
                      </Badge>
                    </div>
                  </div>
                </div>
                {team.description && (
                  <CardDescription className="line-clamp-2">
                    {team.description}
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Team Lead */}
                  <div className="flex items-center gap-3">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={team.lead.avatar || "/placeholder-user.jpg"} />
                      <AvatarFallback className="text-xs">
                        {team.lead.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{team.lead.name}</p>
                      <p className="text-xs text-muted-foreground">Team Lead</p>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-lg font-semibold">{team._count.members}</div>
                      <div className="text-xs text-muted-foreground">Members</div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold">{team._count.tasks}</div>
                      <div className="text-xs text-muted-foreground">Tasks</div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold">{team._count.documents}</div>
                      <div className="text-xs text-muted-foreground">Docs</div>
                    </div>
                  </div>

                  {/* Deadline */}
                  {team.deadline && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="w-4 h-4" />
                      <span>Due {new Date(team.deadline).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}

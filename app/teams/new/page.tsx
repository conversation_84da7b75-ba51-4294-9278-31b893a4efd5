"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Users, Plus, X, Search, UserPlus, Target } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface Developer {
  id: string
  name: string
  email: string
  role: string
  expertise: string[]
  avatar: string
  initials: string
  availability: "available" | "busy" | "vacation"
  currentProjects: number
}

export default function NewTeamPage() {
  const [teamName, setTeamName] = useState("")
  const [description, setDescription] = useState("")
  const [module, setModule] = useState("")
  const [deadline, setDeadline] = useState<Date>()
  const [selectedMembers, setSelectedMembers] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [teamLead, setTeamLead] = useState("")
  const [developers, setDevelopers] = useState<Developer[]>([])
  const [loading, setLoading] = useState(true)

  // Fetch available developers
  useEffect(() => {
    const fetchDevelopers = async () => {
      try {
        const response = await fetch('/api/users')
        if (response.ok) {
          const users = await response.json()
          // Transform API response to match Developer interface
          const transformedUsers = users.map((user: any) => ({
            id: user.id,
            name: user.name || 'Unknown',
            email: user.email,
            role: user.role,
            expertise: user.expertise || [],
            avatar: user.avatar || '/placeholder-user.jpg',
            initials: user.name ? user.name.split(' ').map((n: string) => n[0]).join('') : 'U',
            availability: user.availability?.toLowerCase() || 'available',
            currentProjects: user.currentProjects || 0,
          }))
          setDevelopers(transformedUsers)
        } else {
          console.error('Failed to fetch users')
          setDevelopers([])
        }
      } catch (error) {
        console.error('Error fetching users:', error)
        setDevelopers([])
      } finally {
        setLoading(false)
      }
    }

    fetchDevelopers()
  }, [])

  const filteredDevelopers = developers.filter(
    (dev) =>
      dev.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      dev.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
      dev.expertise.some((skill) => skill.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  const handleMemberToggle = (developerId: string) => {
    setSelectedMembers((prev) =>
      prev.includes(developerId) ? prev.filter((id) => id !== developerId) : [...prev, developerId],
    )
  }

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case "available":
        return "bg-green-100 text-green-800"
      case "busy":
        return "bg-yellow-100 text-yellow-800"
      case "vacation":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const handleCreateTeam = async () => {
    try {
      const response = await fetch('/api/teams', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: teamName,
          description,
          module,
          deadline: deadline?.toISOString(),
          leadId: teamLead,
          memberIds: selectedMembers,
        }),
      })

      if (response.ok) {
        const newTeam = await response.json()
        console.log('Team created successfully:', newTeam)
        // Redirect to team page or show success message
        window.location.href = `/teams/${newTeam.id}`
      } else {
        const error = await response.json()
        console.error('Failed to create team:', error)
        alert('Failed to create team: ' + error.error)
      }
    } catch (error) {
      console.error('Error creating team:', error)
      alert('Error creating team. Please try again.')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Create New Team</h1>
          <p className="text-muted-foreground">Form a collaborative development team for HCM projects</p>
        </div>
        <Button variant="outline" onClick={() => window.history.back()}>
          Cancel
        </Button>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Team Configuration */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                Team Information
              </CardTitle>
              <CardDescription>Define the basic details and scope of your development team</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="team-name">Team Name *</Label>
                <Input
                  id="team-name"
                  placeholder="e.g., Payroll Enhancement Squad"
                  value={teamName}
                  onChange={(e) => setTeamName(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the team's purpose and objectives..."
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="module">HCM Module *</Label>
                  <Select value={module} onValueChange={setModule}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select HCM module" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PY">Payroll (PY)</SelectItem>
                      <SelectItem value="PT">Time Management (PT)</SelectItem>
                      <SelectItem value="PB">Benefits (PB)</SelectItem>
                      <SelectItem value="PA">Personnel Administration (PA)</SelectItem>
                      <SelectItem value="RC">Recruitment (RC)</SelectItem>
                      <SelectItem value="General">General/Cross-Module</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Project Deadline</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !deadline && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {deadline ? format(deadline, "PPP") : "Select deadline"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar mode="single" selected={deadline} onSelect={setDeadline} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Team Member Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Team Members
              </CardTitle>
              <CardDescription>Select developers to join your team</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search developers by name, role, or expertise..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Developer List */}
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-sm text-muted-foreground">Loading developers...</div>
                  </div>
                ) : filteredDevelopers.length === 0 ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-sm text-muted-foreground">No developers found</div>
                  </div>
                ) : (
                  filteredDevelopers.map((developer) => (
                  <div
                    key={developer.id}
                    className={`flex items-center gap-4 p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedMembers.includes(developer.id) ? "bg-primary/5 border-primary" : "hover:bg-muted/50"
                    }`}
                    onClick={() => handleMemberToggle(developer.id)}
                  >
                    <Checkbox checked={selectedMembers.includes(developer.id)} onChange={() => {}} />
                    <Avatar className="w-10 h-10">
                      <AvatarImage src={developer.avatar || "/placeholder.svg"} />
                      <AvatarFallback>{developer.initials}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium">{developer.name}</p>
                        <Badge className={getAvailabilityColor(developer.availability)}>{developer.availability}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{developer.role}</p>
                      <div className="flex flex-wrap gap-1">
                        {developer.expertise.slice(0, 3).map((skill) => (
                          <Badge key={skill} variant="secondary" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                        {developer.expertise.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{developer.expertise.length - 3}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="text-right text-sm text-muted-foreground">
                      <p>{developer.currentProjects} active projects</p>
                    </div>
                  </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Team Summary */}
        <div className="space-y-6">
          {/* Selected Members */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserPlus className="w-5 h-5" />
                Selected Members ({selectedMembers.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedMembers.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-4">No members selected yet</p>
              ) : (
                <div className="space-y-3">
                  {selectedMembers.map((memberId) => {
                    const member = developers.find((dev) => dev.id === memberId)
                    if (!member) return null

                    return (
                      <div key={memberId} className="flex items-center gap-3 p-2 border rounded">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={member.avatar || "/placeholder.svg"} />
                          <AvatarFallback className="text-xs">{member.initials}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <p className="font-medium text-sm">{member.name}</p>
                          <p className="text-xs text-muted-foreground">{member.role}</p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMemberToggle(memberId)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Team Lead Selection */}
          {selectedMembers.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Team Lead</CardTitle>
                <CardDescription>Select a team lead from the selected members</CardDescription>
              </CardHeader>
              <CardContent>
                <Select value={teamLead} onValueChange={setTeamLead}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select team lead" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedMembers.map((memberId) => {
                      const member = developers.find((dev) => dev.id === memberId)
                      if (!member) return null

                      return (
                        <SelectItem key={memberId} value={memberId}>
                          {member.name} - {member.role}
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
              </CardContent>
            </Card>
          )}

          {/* Team Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Team Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Team Size:</span>
                <span className="font-medium">{selectedMembers.length} members</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>HCM Module:</span>
                <span className="font-medium">{module || "Not selected"}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Deadline:</span>
                <span className="font-medium">{deadline ? format(deadline, "MMM dd, yyyy") : "Not set"}</span>
              </div>
              {teamLead && (
                <div className="flex justify-between text-sm">
                  <span>Team Lead:</span>
                  <span className="font-medium">{developers.find((dev) => dev.id === teamLead)?.name}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Create Team Button */}
          <Button
            className="w-full"
            onClick={handleCreateTeam}
            disabled={!teamName || !description || !module || selectedMembers.length === 0}
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Team
          </Button>
        </div>
      </div>
    </div>
  )
}

"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { FileText, Search, Plus, BookOpen, Download, Share, Edit, Eye, Calendar, User } from "lucide-react"

export default function DocumentationPage() {
  const [searchTerm, setSearchTerm] = useState("")

  const documents = [
    {
      id: "DOC-001",
      title: "HCM Payroll Implementation Guide",
      description: "Comprehensive guide for implementing payroll schemas in SAP HCM",
      type: "Implementation Guide",
      module: "PY",
      author: "<PERSON>",
      lastModified: "2024-01-10",
      version: "2.1",
      status: "Published",
      downloads: 45,
    },
    {
      id: "DOC-002",
      title: "Time Management Configuration Manual",
      description: "Step-by-step configuration for time recording and attendance",
      type: "Configuration Manual",
      module: "PT",
      author: "<PERSON>",
      lastModified: "2024-01-08",
      version: "1.5",
      status: "Draft",
      downloads: 23,
    },
    {
      id: "DOC-003",
      title: "Benefits Administration API Reference",
      description: "Technical documentation for benefits calculation APIs",
      type: "API Reference",
      module: "PB",
      author: "Mike Davis",
      lastModified: "2024-01-12",
      version: "3.0",
      status: "Published",
      downloads: 67,
    },
    {
      id: "DOC-004",
      title: "Custom ABAP Development Standards",
      description: "Coding standards and best practices for HCM customizations",
      type: "Standards Document",
      module: "General",
      author: "Lisa Wilson",
      lastModified: "2024-01-05",
      version: "1.2",
      status: "Published",
      downloads: 89,
    },
  ]

  const templates = [
    {
      name: "Technical Specification Template",
      description: "Standard template for technical specifications",
      category: "Development",
    },
    {
      name: "User Manual Template",
      description: "Template for end-user documentation",
      category: "User Guide",
    },
    {
      name: "API Documentation Template",
      description: "Template for API reference documentation",
      category: "Technical",
    },
    {
      name: "Configuration Guide Template",
      description: "Template for system configuration guides",
      category: "Configuration",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Published":
        return "bg-green-100 text-green-800"
      case "Draft":
        return "bg-yellow-100 text-yellow-800"
      case "Review":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Documentation</h1>
          <p className="text-muted-foreground">Manage technical documentation and knowledge base</p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          New Document
        </Button>
      </div>

      <Tabs defaultValue="documents" className="space-y-6">
        <TabsList>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="knowledge">Knowledge Base</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <Input
                      placeholder="Search documentation..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Button variant="outline">Filter</Button>
              </div>
            </CardContent>
          </Card>

          {/* Documents List */}
          <div className="grid gap-4">
            {documents.map((doc) => (
              <Card key={doc.id} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2 flex-1">
                      <div className="flex items-center gap-2">
                        <FileText className="w-5 h-5 text-muted-foreground" />
                        <h3 className="font-semibold">{doc.title}</h3>
                        <Badge variant="outline">{doc.module}</Badge>
                        <Badge className={getStatusColor(doc.status)}>{doc.status}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{doc.description}</p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <User className="w-3 h-3" />
                          {doc.author}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {doc.lastModified}
                        </div>
                        <div>Version {doc.version}</div>
                        <div>{doc.downloads} downloads</div>
                      </div>
                    </div>
                    <div className="flex gap-2 ml-4">
                      <Button variant="ghost" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Download className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Share className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          {/* Document Templates */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="w-5 h-5" />
                Document Templates
              </CardTitle>
              <CardDescription>Pre-built templates for common documentation types</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {templates.map((template, index) => (
                  <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="pt-6">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{template.name}</h4>
                          <Badge variant="outline">{template.category}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{template.description}</p>
                        <div className="flex gap-2 pt-2">
                          <Button size="sm" variant="outline">
                            <Eye className="w-3 h-3 mr-1" />
                            Preview
                          </Button>
                          <Button size="sm">
                            <Plus className="w-3 h-3 mr-1" />
                            Use Template
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="knowledge" className="space-y-6">
          {/* Knowledge Base */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="w-5 h-5" />
                Knowledge Base
              </CardTitle>
              <CardDescription>Searchable knowledge base for common issues and solutions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input placeholder="Search knowledge base..." className="pl-10" />
                </div>

                <div className="grid gap-4">
                  <Card className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="pt-4">
                      <h4 className="font-medium mb-2">How to configure payroll schemas for multiple countries?</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        Step-by-step guide for setting up country-specific payroll schemas in SAP HCM...
                      </p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Badge variant="outline">Payroll</Badge>
                        <span>Updated 3 days ago</span>
                        <span>•</span>
                        <span>15 views</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="pt-4">
                      <h4 className="font-medium mb-2">Common ABAP performance issues in HCM modules</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        Identify and resolve performance bottlenecks in HCM ABAP programs...
                      </p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Badge variant="outline">Performance</Badge>
                        <span>Updated 1 week ago</span>
                        <span>•</span>
                        <span>42 views</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="pt-4">
                      <h4 className="font-medium mb-2">Time management integration with external systems</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        Best practices for integrating SAP HCM time management with third-party systems...
                      </p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Badge variant="outline">Integration</Badge>
                        <span>Updated 2 weeks ago</span>
                        <span>•</span>
                        <span>28 views</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

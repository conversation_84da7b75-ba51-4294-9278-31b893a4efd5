"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import {
  Users,
  Search,
  Filter,
  Plus,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Code2,
  GitBranch,
  Target,
  Clock,
  Star,
  Award,
  Building,
  UserPlus,
  Settings,
  MessageSquare,
  Video,
  Mail as MailIcon,
  ExternalLink,
  Globe,
  Linkedin,
  Github,
} from "lucide-react"
import Link from "next/link"

export default function TeamPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [roleFilter, setRoleFilter] = useState("all")
  const [moduleFilter, setModuleFilter] = useState("all")

  const teamMembers = [
    {
      id: "john-smith",
      name: "<PERSON>",
      role: "Senior ABAP Developer",
      avatar: "/placeholder.svg?height=64&width=64",
      initials: "JS",
      email: "<EMAIL>",
      phone: "+****************",
      location: "New York, NY",
      joinDate: "2022-03-15",
      expertise: ["PA", "PT", "PY"],
      skills: ["ABAP", "SAP HCM", "Payroll", "Time Management", "JavaScript", "React"],
      currentProjects: ["Global Payroll Enhancement", "Time Recording API"],
      completedProjects: 23,
      avgTaskDuration: "2.8 days",
      productivity: 87,
      availability: "Available",
      status: "online",
      bio: "Senior ABAP developer with 8+ years of experience in SAP HCM modules. Specialized in payroll processing and time management solutions.",
      certifications: ["SAP Certified Development Associate", "SAP HCM Specialist"],
      github: "https://github.com/johnsmith",
      linkedin: "https://linkedin.com/in/johnsmith",
    },
    {
      id: "sarah-johnson",
      name: "Sarah Johnson",
      role: "ABAP Developer",
      avatar: "/placeholder.svg?height=64&width=64",
      initials: "SJ",
      email: "<EMAIL>",
      phone: "+****************",
      location: "San Francisco, CA",
      joinDate: "2023-01-10",
      expertise: ["PT", "PB"],
      skills: ["ABAP", "SAP HCM", "Benefits", "Time Management", "Python", "Django"],
      currentProjects: ["Benefits Calculation Update", "ESS Portal Enhancement"],
      completedProjects: 18,
      avgTaskDuration: "3.1 days",
      productivity: 82,
      availability: "Available",
      status: "online",
      bio: "ABAP developer focused on benefits administration and employee self-service solutions. Passionate about user experience and automation.",
      certifications: ["SAP Certified Development Associate"],
      github: "https://github.com/sarahjohnson",
      linkedin: "https://linkedin.com/in/sarahjohnson",
    },
    {
      id: "mike-davis",
      name: "Mike Davis",
      role: "Technical Lead",
      avatar: "/placeholder.svg?height=64&width=64",
      initials: "MD",
      email: "<EMAIL>",
      phone: "+****************",
      location: "Austin, TX",
      joinDate: "2021-08-22",
      expertise: ["PB", "RC", "OM"],
      skills: ["ABAP", "SAP HCM", "Recruitment", "Organizational Management", "Java", "Spring Boot"],
      currentProjects: ["Recruitment Automation", "Org Structure Redesign"],
      completedProjects: 15,
      avgTaskDuration: "4.2 days",
      productivity: 91,
      availability: "Available",
      status: "online",
      bio: "Technical lead with extensive experience in SAP HCM architecture and system integration. Expert in recruitment and organizational management modules.",
      certifications: ["SAP Certified Development Professional", "SAP HCM Consultant"],
      github: "https://github.com/mikedavis",
      linkedin: "https://linkedin.com/in/mikedavis",
    },
    {
      id: "lisa-wilson",
      name: "Lisa Wilson",
      role: "Senior Developer",
      avatar: "/placeholder.svg?height=64&width=64",
      initials: "LW",
      email: "<EMAIL>",
      phone: "+****************",
      location: "Chicago, IL",
      joinDate: "2022-11-05",
      expertise: ["PA", "RC"],
      skills: ["ABAP", "SAP HCM", "Personnel Administration", "Recruitment", "TypeScript", "Next.js"],
      currentProjects: ["Employee Onboarding System", "Mobile ESS App"],
      completedProjects: 21,
      avgTaskDuration: "2.9 days",
      productivity: 85,
      availability: "Available",
      status: "online",
      bio: "Senior developer specializing in personnel administration and recruitment solutions. Experienced in modern web technologies and mobile development.",
      certifications: ["SAP Certified Development Associate", "SAP Mobile Solutions"],
      github: "https://github.com/lisawilson",
      linkedin: "https://linkedin.com/in/lisawilson",
    },
    {
      id: "tom-brown",
      name: "Tom Brown",
      role: "ABAP Developer",
      avatar: "/placeholder.svg?height=64&width=64",
      initials: "TB",
      email: "<EMAIL>",
      phone: "+****************",
      location: "Seattle, WA",
      joinDate: "2023-06-12",
      expertise: ["PA", "PT"],
      skills: ["ABAP", "SAP HCM", "Personnel Administration", "Time Management", "Node.js", "Express"],
      currentProjects: ["Time Sheet Automation", "Leave Management System"],
      completedProjects: 12,
      avgTaskDuration: "3.5 days",
      productivity: 78,
      availability: "Available",
      status: "online",
      bio: "ABAP developer with strong focus on time management and personnel administration. Skilled in backend development and API integration.",
      certifications: ["SAP Certified Development Associate"],
      github: "https://github.com/tombrown",
      linkedin: "https://linkedin.com/in/tombrown",
    },
  ]

  const teams = [
    {
      id: "payroll-team",
      name: "Payroll Enhancement Squad",
      description: "Specialized team for payroll processing and calculation enhancements",
      members: ["john-smith", "sarah-johnson"],
      lead: "John Smith",
      projects: ["Global Payroll Enhancement", "Tax Calculation Updates"],
      status: "Active",
    },
    {
      id: "time-management-team",
      name: "Time Management Team",
      description: "Focused on time recording and attendance solutions",
      members: ["sarah-johnson", "tom-brown"],
      lead: "Sarah Johnson",
      projects: ["Time Recording API", "Time Sheet Automation"],
      status: "Active",
    },
    {
      id: "recruitment-team",
      name: "Recruitment Solutions",
      description: "Recruitment process automation and candidate management",
      members: ["mike-davis", "lisa-wilson"],
      lead: "Mike Davis",
      projects: ["Recruitment Automation", "Candidate Portal"],
      status: "Active",
    },
  ]

  const filteredMembers = teamMembers.filter((member) => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesRole = roleFilter === "all" || member.role.toLowerCase().includes(roleFilter.toLowerCase())
    const matchesModule = moduleFilter === "all" || member.expertise.includes(moduleFilter)
    
    return matchesSearch && matchesRole && matchesModule
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-green-500"
      case "away":
        return "bg-yellow-500"
      case "busy":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case "Available":
        return "bg-green-100 text-green-800"
      case "Busy":
        return "bg-red-100 text-red-800"
      case "Away":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getProductivityColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 80) return "text-blue-600"
    if (score >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Team Management</h1>
          <p className="text-muted-foreground">Manage your SAP HCM development team and collaboration</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <UserPlus className="w-4 h-4 mr-2" />
            Invite Member
          </Button>
          <Button asChild>
            <Link href="/teams/new">
              <Plus className="w-4 h-4 mr-2" />
              Create Team
            </Link>
          </Button>
        </div>
      </div>

      {/* Team Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{teamMembers.length}</div>
            <p className="text-xs text-muted-foreground">
              +2 from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Teams</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{teams.length}</div>
            <p className="text-xs text-muted-foreground">
              All teams active
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Productivity</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(teamMembers.reduce((acc, member) => acc + member.productivity, 0) / teamMembers.length)}%
            </div>
            <p className="text-xs text-muted-foreground">
              +5% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Online Now</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {teamMembers.filter(member => member.status === "online").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Available for collaboration
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="members" className="space-y-6">
        <TabsList>
          <TabsTrigger value="members">Team Members</TabsTrigger>
          <TabsTrigger value="teams">Teams</TabsTrigger>
          <TabsTrigger value="skills">Skills Matrix</TabsTrigger>
        </TabsList>

        <TabsContent value="members" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex gap-4 flex-wrap">
                <div className="flex-1 min-w-[200px]">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <Input
                      placeholder="Search team members..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Roles</SelectItem>
                    <SelectItem value="senior">Senior</SelectItem>
                    <SelectItem value="developer">Developer</SelectItem>
                    <SelectItem value="lead">Technical Lead</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={moduleFilter} onValueChange={setModuleFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Module" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Modules</SelectItem>
                    <SelectItem value="PA">PA - Personnel Admin</SelectItem>
                    <SelectItem value="PT">PT - Time Management</SelectItem>
                    <SelectItem value="PB">PB - Benefits</SelectItem>
                    <SelectItem value="RC">RC - Recruitment</SelectItem>
                    <SelectItem value="PY">PY - Payroll</SelectItem>
                    <SelectItem value="OM">OM - Org Management</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Team Members Grid */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredMembers.map((member) => (
              <Card key={member.id} className="relative">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback>{member.initials}</AvatarFallback>
                        </Avatar>
                        <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${getStatusColor(member.status)}`} />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{member.name}</CardTitle>
                        <CardDescription>{member.role}</CardDescription>
                      </div>
                    </div>
                    <Badge className={getAvailabilityColor(member.availability)}>
                      {member.availability}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Contact Info */}
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Mail className="w-4 h-4" />
                      <a href={`mailto:${member.email}`} className="hover:text-primary">
                        {member.email}
                      </a>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="w-4 h-4" />
                      {member.location}
                    </div>
                  </div>

                  {/* Expertise */}
                  <div>
                    <h4 className="text-sm font-medium mb-2">HCM Expertise</h4>
                    <div className="flex flex-wrap gap-1">
                      {member.expertise.map((module) => (
                        <Badge key={module} variant="outline" className="text-xs">
                          {module}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Current Projects */}
                  <div>
                    <h4 className="text-sm font-medium mb-2">Current Projects</h4>
                    <div className="space-y-1">
                      {member.currentProjects.map((project, index) => (
                        <div key={index} className="text-sm text-muted-foreground">
                          • {project}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Performance Metrics */}
                  <div className="grid grid-cols-2 gap-4 pt-2 border-t">
                    <div className="text-center">
                      <div className="text-sm font-medium">{member.completedProjects}</div>
                      <div className="text-xs text-muted-foreground">Projects</div>
                    </div>
                    <div className="text-center">
                      <div className={`text-sm font-medium ${getProductivityColor(member.productivity)}`}>
                        {member.productivity}%
                      </div>
                      <div className="text-xs text-muted-foreground">Productivity</div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2 border-t">
                    <Button variant="outline" size="sm" className="flex-1">
                      <MessageSquare className="w-4 h-4 mr-1" />
                      Message
                    </Button>
                    <Button variant="outline" size="sm">
                      <Video className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="teams" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {teams.map((team) => (
              <Card key={team.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle>{team.name}</CardTitle>
                      <CardDescription>{team.description}</CardDescription>
                    </div>
                    <Badge variant="outline">{team.status}</Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Team Lead</h4>
                    <div className="text-sm text-muted-foreground">{team.lead}</div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Members ({team.members.length})</h4>
                    <div className="flex -space-x-2">
                      {team.members.map((memberId) => {
                        const member = teamMembers.find(m => m.id === memberId)
                        return member ? (
                          <Avatar key={memberId} className="h-8 w-8 border-2 border-background">
                            <AvatarImage src={member.avatar} />
                            <AvatarFallback className="text-xs">{member.initials}</AvatarFallback>
                          </Avatar>
                        ) : null
                      })}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">Active Projects</h4>
                    <div className="space-y-1">
                      {team.projects.map((project, index) => (
                        <div key={index} className="text-sm text-muted-foreground">
                          • {project}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2 pt-2 border-t">
                    <Button variant="outline" size="sm" className="flex-1">
                      View Details
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="skills" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Skills Matrix</CardTitle>
              <CardDescription>Team expertise across SAP HCM modules and technologies</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* HCM Modules */}
                <div>
                  <h3 className="text-lg font-medium mb-4">SAP HCM Modules</h3>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {["PA", "PT", "PB", "RC", "PY", "OM"].map((module) => {
                      const experts = teamMembers.filter(member => member.expertise.includes(module))
                      const percentage = Math.round((experts.length / teamMembers.length) * 100)
                      return (
                        <div key={module} className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="font-medium">{module}</span>
                            <span className="text-muted-foreground">{experts.length} experts</span>
                          </div>
                          <Progress value={percentage} className="h-2" />
                          <div className="text-xs text-muted-foreground">
                            {experts.map(expert => expert.name).join(", ")}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>

                {/* Technical Skills */}
                <div>
                  <h3 className="text-lg font-medium mb-4">Technical Skills</h3>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {["ABAP", "SAP HCM", "JavaScript", "React", "Python", "Java", "TypeScript", "Node.js"].map((skill) => {
                      const experts = teamMembers.filter(member => member.skills.includes(skill))
                      const percentage = Math.round((experts.length / teamMembers.length) * 100)
                      return (
                        <div key={skill} className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="font-medium">{skill}</span>
                            <span className="text-muted-foreground">{experts.length} members</span>
                          </div>
                          <Progress value={percentage} className="h-2" />
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 
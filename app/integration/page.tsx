"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Progress } from "@/components/ui/progress"
import { Database, Settings, CheckCircle2, AlertCircle, RefreshCw, Zap, Shield, Clock, Activity } from "lucide-react"

export default function IntegrationPage() {
  const sapConnections = [
    {
      name: "SAP HCM Production",
      system: "PRD",
      client: "100",
      status: "Connected",
      lastSync: "2 minutes ago",
      modules: ["PA", "PT", "PY", "PB"],
      health: 98,
    },
    {
      name: "SAP HCM Development",
      system: "DEV",
      client: "100",
      status: "Connected",
      lastSync: "5 minutes ago",
      modules: ["PA", "PT", "PY"],
      health: 95,
    },
    {
      name: "SAP HCM Quality",
      system: "QAS",
      client: "100",
      status: "Disconnected",
      lastSync: "2 hours ago",
      modules: ["PA", "PT"],
      health: 0,
    },
  ]

  const integrationServices = [
    {
      name: "Real-time Data Sync",
      description: "Synchronize employee data in real-time",
      enabled: true,
      status: "Active",
      lastRun: "1 minute ago",
    },
    {
      name: "Payroll Data Export",
      description: "Automated payroll data export to external systems",
      enabled: true,
      status: "Active",
      lastRun: "30 minutes ago",
    },
    {
      name: "Time Sheet Integration",
      description: "Import time sheets from external time tracking systems",
      enabled: false,
      status: "Inactive",
      lastRun: "Never",
    },
    {
      name: "Benefits Enrollment Sync",
      description: "Synchronize benefits enrollment data",
      enabled: true,
      status: "Active",
      lastRun: "1 hour ago",
    },
  ]

  const apiEndpoints = [
    {
      name: "Employee Data API",
      endpoint: "/api/v1/employees",
      method: "GET",
      status: "Healthy",
      responseTime: "45ms",
      requests24h: 1247,
    },
    {
      name: "Payroll Calculation API",
      endpoint: "/api/v1/payroll/calculate",
      method: "POST",
      status: "Healthy",
      responseTime: "120ms",
      requests24h: 89,
    },
    {
      name: "Time Management API",
      endpoint: "/api/v1/time-entries",
      method: "GET/POST",
      status: "Warning",
      responseTime: "250ms",
      requests24h: 456,
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Connected":
      case "Active":
      case "Healthy":
        return "text-green-600"
      case "Disconnected":
      case "Inactive":
        return "text-red-600"
      case "Warning":
        return "text-yellow-600"
      default:
        return "text-gray-600"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Connected":
      case "Active":
      case "Healthy":
        return <CheckCircle2 className="w-4 h-4 text-green-600" />
      case "Disconnected":
      case "Inactive":
        return <AlertCircle className="w-4 h-4 text-red-600" />
      case "Warning":
        return <AlertCircle className="w-4 h-4 text-yellow-600" />
      default:
        return <AlertCircle className="w-4 h-4 text-gray-600" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">SAP HCM Integration</h1>
          <p className="text-muted-foreground">Manage connections and integrations with SAP HCM systems</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh All
          </Button>
          <Button>
            <Settings className="w-4 h-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      {/* SAP System Connections */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            SAP System Connections
          </CardTitle>
          <CardDescription>Status of connections to SAP HCM systems</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sapConnections.map((connection, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(connection.status)}
                    <div>
                      <h4 className="font-medium">{connection.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        System: {connection.system} | Client: {connection.client}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge
                      className={
                        connection.status === "Connected" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                      }
                    >
                      {connection.status}
                    </Badge>
                    <p className="text-xs text-muted-foreground mt-1">Last sync: {connection.lastSync}</p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex gap-2">
                    {connection.modules.map((module) => (
                      <Badge key={module} variant="outline">
                        {module}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">Health:</span>
                    <Progress value={connection.health} className="w-20 h-2" />
                    <span className="text-sm font-medium">{connection.health}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Integration Services */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Integration Services
          </CardTitle>
          <CardDescription>Manage automated integration services and data synchronization</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {integrationServices.map((service, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Switch checked={service.enabled} />
                  <div>
                    <h4 className="font-medium">{service.name}</h4>
                    <p className="text-sm text-muted-foreground">{service.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-2 mb-1">
                    {getStatusIcon(service.status)}
                    <Badge
                      className={
                        service.status === "Active" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                      }
                    >
                      {service.status}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">Last run: {service.lastRun}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* API Endpoints */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            API Endpoints
          </CardTitle>
          <CardDescription>Monitor API endpoint health and performance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {apiEndpoints.map((api, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(api.status)}
                    <div>
                      <h4 className="font-medium">{api.name}</h4>
                      <p className="text-sm text-muted-foreground font-mono">{api.endpoint}</p>
                    </div>
                  </div>
                  <Badge variant="outline">{api.method}</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3 text-muted-foreground" />
                      <span>Response: {api.responseTime}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Activity className="w-3 h-3 text-muted-foreground" />
                      <span>24h Requests: {api.requests24h.toLocaleString()}</span>
                    </div>
                  </div>
                  <Badge
                    className={
                      api.status === "Healthy" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
                    }
                  >
                    {api.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Security & Compliance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Security & Compliance
          </CardTitle>
          <CardDescription>Security settings and compliance status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">SSL/TLS Encryption</span>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600">Enabled</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">API Authentication</span>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600">OAuth 2.0</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Data Encryption</span>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600">AES-256</span>
                </div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">GDPR Compliance</span>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600">Compliant</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Audit Logging</span>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600">Active</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Access Control</span>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600">Role-based</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

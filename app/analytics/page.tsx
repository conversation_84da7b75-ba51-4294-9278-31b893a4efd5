"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  BarChart3,
  TrendingUp,
  Users,
  Clock,
  CheckCircle2,
  AlertTriangle,
  Code2,
  GitBranch,
  Calendar,
  Target,
  Activity,
  PieChart,
  LineChart,
  BarChart,
  Download,
  RefreshCw,
  Filter,
  CheckSquare,
} from "lucide-react"

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState("30d")
  const [isLoading, setIsLoading] = useState(false)

  const refreshData = () => {
    setIsLoading(true)
    setTimeout(() => setIsLoading(false), 1000)
  }

  const developmentMetrics = {
    totalTasks: 156,
    completedTasks: 89,
    inProgressTasks: 42,
    pendingTasks: 25,
    completionRate: 57,
    averageTaskDuration: "3.2 days",
    codeQuality: 92,
    testCoverage: 78,
  }

  const teamPerformance = [
    {
      name: "John Smith",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "JS",
      role: "Senior ABAP Developer",
      tasksCompleted: 23,
      avgTaskDuration: "2.8 days",
      codeQuality: 94,
      productivity: 87,
    },
    {
      name: "Sarah Johnson",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "SJ",
      role: "ABAP Developer",
      tasksCompleted: 18,
      avgTaskDuration: "3.1 days",
      codeQuality: 89,
      productivity: 82,
    },
    {
      name: "Mike Davis",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "MD",
      role: "Technical Lead",
      tasksCompleted: 15,
      avgTaskDuration: "4.2 days",
      codeQuality: 96,
      productivity: 91,
    },
    {
      name: "Lisa Wilson",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "LW",
      role: "Senior Developer",
      tasksCompleted: 21,
      avgTaskDuration: "2.9 days",
      codeQuality: 91,
      productivity: 85,
    },
  ]

  const moduleStats = [
    { module: "PA - Personnel Administration", tasks: 45, completion: 78, avgHours: 32 },
    { module: "PT - Personnel Time Management", tasks: 38, completion: 82, avgHours: 28 },
    { module: "PB - Personnel Benefits", tasks: 29, completion: 69, avgHours: 24 },
    { module: "RC - Recruitment", tasks: 22, completion: 73, avgHours: 40 },
    { module: "PY - Payroll", tasks: 18, completion: 89, avgHours: 48 },
    { module: "OM - Organizational Management", tasks: 4, completion: 50, avgHours: 36 },
  ]

  const recentActivity = [
    {
      type: "task_completed",
      title: "Payroll Schema Enhancement completed",
      user: "John Smith",
      time: "2 hours ago",
      module: "PY",
    },
    {
      type: "code_review",
      title: "Code review approved for Time Management API",
      user: "Mike Davis",
      time: "4 hours ago",
      module: "PT",
    },
    {
      type: "task_created",
      title: "New task: Benefits calculation update",
      user: "Sarah Johnson",
      time: "6 hours ago",
      module: "PB",
    },
    {
      type: "deployment",
      title: "Deployed to QAS environment",
      user: "Lisa Wilson",
      time: "1 day ago",
      module: "PA",
    },
  ]

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "task_completed":
        return <CheckCircle2 className="w-4 h-4 text-green-600" />
      case "code_review":
        return <Code2 className="w-4 h-4 text-blue-600" />
      case "task_created":
        return <Target className="w-4 h-4 text-orange-600" />
      case "deployment":
        return <GitBranch className="w-4 h-4 text-purple-600" />
      default:
        return <Activity className="w-4 h-4" />
    }
  }

  const getProductivityColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 80) return "text-blue-600"
    if (score >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-muted-foreground">Development metrics and team performance insights</p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={refreshData} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
            <CheckSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{developmentMetrics.totalTasks}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{developmentMetrics.completionRate}%</div>
            <p className="text-xs text-muted-foreground">
              +5% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Code Quality</CardTitle>
            <Code2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{developmentMetrics.codeQuality}%</div>
            <p className="text-xs text-muted-foreground">
              +3% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Test Coverage</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{developmentMetrics.testCoverage}%</div>
            <p className="text-xs text-muted-foreground">
              +8% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="team">Team Performance</TabsTrigger>
          <TabsTrigger value="modules">Module Analysis</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Task Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="w-5 h-5" />
                  Task Status Distribution
                </CardTitle>
                <CardDescription>Current task breakdown by status</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full" />
                      <span className="text-sm">Completed</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{developmentMetrics.completedTasks}</span>
                      <span className="text-xs text-muted-foreground">
                        {Math.round((developmentMetrics.completedTasks / developmentMetrics.totalTasks) * 100)}%
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full" />
                      <span className="text-sm">In Progress</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{developmentMetrics.inProgressTasks}</span>
                      <span className="text-xs text-muted-foreground">
                        {Math.round((developmentMetrics.inProgressTasks / developmentMetrics.totalTasks) * 100)}%
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                      <span className="text-sm">Pending</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{developmentMetrics.pendingTasks}</span>
                      <span className="text-xs text-muted-foreground">
                        {Math.round((developmentMetrics.pendingTasks / developmentMetrics.totalTasks) * 100)}%
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Performance Trends */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="w-5 h-5" />
                  Performance Trends
                </CardTitle>
                <CardDescription>Key metrics over time</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Completion Rate</span>
                      <span>{developmentMetrics.completionRate}%</span>
                    </div>
                    <Progress value={developmentMetrics.completionRate} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Code Quality</span>
                      <span>{developmentMetrics.codeQuality}%</span>
                    </div>
                    <Progress value={developmentMetrics.codeQuality} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Test Coverage</span>
                      <span>{developmentMetrics.testCoverage}%</span>
                    </div>
                    <Progress value={developmentMetrics.testCoverage} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="team" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Team Performance
              </CardTitle>
              <CardDescription>Individual developer metrics and productivity</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {teamPerformance.map((member, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <Avatar>
                        <AvatarImage src={member.avatar} />
                        <AvatarFallback>{member.initials}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{member.name}</div>
                        <div className="text-sm text-muted-foreground">{member.role}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-6">
                      <div className="text-center">
                        <div className="text-sm font-medium">{member.tasksCompleted}</div>
                        <div className="text-xs text-muted-foreground">Tasks</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-medium">{member.avgTaskDuration}</div>
                        <div className="text-xs text-muted-foreground">Avg Duration</div>
                      </div>
                      <div className="text-center">
                        <div className={`text-sm font-medium ${getProductivityColor(member.productivity)}`}>
                          {member.productivity}%
                        </div>
                        <div className="text-xs text-muted-foreground">Productivity</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-medium">{member.codeQuality}%</div>
                        <div className="text-xs text-muted-foreground">Quality</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="modules" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart className="w-5 h-5" />
                Module Analysis
              </CardTitle>
              <CardDescription>Performance metrics by HCM module</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {moduleStats.map((module, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{module.module}</div>
                      <div className="text-sm text-muted-foreground">
                        {module.tasks} tasks • {module.avgHours} avg hours
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <div className="text-sm font-medium">{module.completion}%</div>
                        <div className="text-xs text-muted-foreground">Completion</div>
                      </div>
                      <Progress value={module.completion} className="w-20 h-2" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>Latest development activities and updates</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                    <div className="flex items-center justify-center w-8 h-8 bg-muted rounded-full">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium">{activity.title}</div>
                      <div className="text-sm text-muted-foreground">
                        by {activity.user} • {activity.time}
                      </div>
                    </div>
                    <Badge variant="outline">{activity.module}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 
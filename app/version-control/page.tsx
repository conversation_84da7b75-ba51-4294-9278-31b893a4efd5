"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { GitBranch, GitCommit, GitMerge, Plus, Search, Calendar, User, FileText, Download, Upload } from "lucide-react"

export default function VersionControlPage() {
  const repositories = [
    {
      name: "sap-hcm-payroll-enhancements",
      description: "Custom payroll schema implementations and enhancements",
      lastCommit: "Fix payroll calculation for overtime hours",
      author: "<PERSON>",
      date: "2 hours ago",
      branch: "main",
      commits: 47,
      contributors: 3,
    },
    {
      name: "hcm-time-management-custom",
      description: "Time recording and attendance management customizations",
      lastCommit: "Add shift differential calculation logic",
      author: "<PERSON>",
      date: "1 day ago",
      branch: "development",
      commits: 23,
      contributors: 2,
    },
    {
      name: "benefits-administration-api",
      description: "Benefits enrollment and calculation API development",
      lastCommit: "Update benefits calculation for new healthcare plans",
      author: "<PERSON>",
      date: "3 days ago",
      branch: "feature/healthcare-update",
      commits: 15,
      contributors: 1,
    },
  ]

  const recentCommits = [
    {
      id: "a1b2c3d",
      message: "Fix payroll calculation for overtime hours",
      author: "John Smith",
      date: "2 hours ago",
      repository: "sap-hcm-payroll-enhancements",
      branch: "main",
      filesChanged: 3,
    },
    {
      id: "e4f5g6h",
      message: "Add shift differential calculation logic",
      author: "Sarah Johnson",
      date: "1 day ago",
      repository: "hcm-time-management-custom",
      branch: "development",
      filesChanged: 5,
    },
    {
      id: "i7j8k9l",
      message: "Update benefits calculation for new healthcare plans",
      author: "Mike Davis",
      date: "3 days ago",
      repository: "benefits-administration-api",
      branch: "feature/healthcare-update",
      filesChanged: 2,
    },
    {
      id: "m0n1o2p",
      message: "Refactor employee data validation functions",
      author: "Lisa Wilson",
      date: "1 week ago",
      repository: "sap-hcm-payroll-enhancements",
      branch: "main",
      filesChanged: 4,
    },
  ]

  const pullRequests = [
    {
      id: 12,
      title: "Implement new benefits calculation logic",
      author: "Mike Davis",
      repository: "benefits-administration-api",
      source: "feature/new-benefits",
      target: "main",
      status: "Open",
      created: "2 days ago",
      comments: 3,
      approvals: 1,
    },
    {
      id: 11,
      title: "Fix time recording validation issues",
      author: "Sarah Johnson",
      repository: "hcm-time-management-custom",
      source: "bugfix/validation-fix",
      target: "development",
      status: "Approved",
      created: "1 week ago",
      comments: 7,
      approvals: 2,
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Open":
        return "bg-blue-100 text-blue-800"
      case "Approved":
        return "bg-green-100 text-green-800"
      case "Merged":
        return "bg-purple-100 text-purple-800"
      case "Closed":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Version Control</h1>
          <p className="text-muted-foreground">Manage ABAP code repositories and version control</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Clone Repository
          </Button>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            New Repository
          </Button>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input placeholder="Search repositories, commits, or pull requests..." className="pl-10" />
          </div>
        </CardContent>
      </Card>

      {/* Repositories */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="w-5 h-5" />
            Repositories
          </CardTitle>
          <CardDescription>ABAP code repositories and their current status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {repositories.map((repo, index) => (
              <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-3">
                  <div className="space-y-1">
                    <h4 className="font-semibold text-lg">{repo.name}</h4>
                    <p className="text-sm text-muted-foreground">{repo.description}</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <GitBranch className="w-3 h-3 mr-1" />
                      Clone
                    </Button>
                    <Button variant="outline" size="sm">
                      <Upload className="w-3 h-3 mr-1" />
                      Push
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <GitCommit className="w-4 h-4 text-muted-foreground" />
                    <span className="font-medium">{repo.lastCommit}</span>
                  </div>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      {repo.author}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {repo.date}
                    </div>
                    <Badge variant="outline">{repo.branch}</Badge>
                    <span>{repo.commits} commits</span>
                    <span>{repo.contributors} contributors</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Recent Commits */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GitCommit className="w-5 h-5" />
              Recent Commits
            </CardTitle>
            <CardDescription>Latest commits across all repositories</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentCommits.map((commit, index) => (
                <div key={index} className="border-l-2 border-muted pl-4 space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-mono text-sm bg-muted px-2 py-1 rounded">{commit.id}</span>
                    <Badge variant="outline">{commit.branch}</Badge>
                  </div>
                  <p className="font-medium text-sm">{commit.message}</p>
                  <div className="flex items-center gap-3 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      {commit.author}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {commit.date}
                    </div>
                    <div className="flex items-center gap-1">
                      <FileText className="w-3 h-3" />
                      {commit.filesChanged} files
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">{commit.repository}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Pull Requests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GitMerge className="w-5 h-5" />
              Pull Requests
            </CardTitle>
            <CardDescription>Active pull requests requiring review</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pullRequests.map((pr, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">#{pr.id}</span>
                        <Badge className={getStatusColor(pr.status)}>{pr.status}</Badge>
                      </div>
                      <h4 className="font-medium">{pr.title}</h4>
                      <p className="text-sm text-muted-foreground">{pr.repository}</p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        {pr.author}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {pr.created}
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <span>{pr.comments} comments</span>
                      <span>{pr.approvals} approvals</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 text-sm">
                    <Badge variant="outline">{pr.source}</Badge>
                    <GitMerge className="w-4 h-4 text-muted-foreground" />
                    <Badge variant="outline">{pr.target}</Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

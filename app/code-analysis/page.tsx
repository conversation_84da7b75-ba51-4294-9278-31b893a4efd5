"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Code2, Upload, Search, AlertTriangle, CheckCircle2, Info, FileText, BarChart3, Zap } from "lucide-react"

export default function CodeAnalysisPage() {
  const [analysisResults, setAnalysisResults] = useState(null)

  const codeIssues = [
    {
      type: "error",
      severity: "High",
      line: 45,
      message: "Undefined variable 'lv_temp' used in calculation",
      file: "ZHR_PAYROLL_CALC.abap",
      suggestion: "Declare variable before use: DATA: lv_temp TYPE p DECIMALS 2.",
    },
    {
      type: "warning",
      severity: "Medium",
      line: 128,
      message: "Hardcoded value found - consider using constants",
      file: "ZHR_TIME_MGMT.abap",
      suggestion: "Replace '8.0' with constant: CONSTANTS: gc_standard_hours TYPE p VALUE '8.0'.",
    },
    {
      type: "info",
      severity: "Low",
      line: 67,
      message: "Consider using newer ABAP syntax for better performance",
      file: "ZHR_BENEFITS.abap",
      suggestion: "Replace LOOP AT with VALUE or REDUCE operator where applicable.",
    },
  ]

  const codeMetrics = {
    totalLines: 2847,
    codeLines: 2156,
    commentLines: 421,
    blankLines: 270,
    complexity: 15.2,
    maintainabilityIndex: 78,
    testCoverage: 65,
  }

  const getIssueIcon = (type: string) => {
    switch (type) {
      case "error":
        return <AlertTriangle className="w-4 h-4 text-red-600" />
      case "warning":
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />
      case "info":
        return <Info className="w-4 h-4 text-blue-600" />
      default:
        return <Info className="w-4 h-4" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "High":
        return "bg-red-100 text-red-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Low":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Code Analysis</h1>
          <p className="text-muted-foreground">Analyze ABAP code quality, performance, and best practices</p>
        </div>
        <Button>
          <Zap className="w-4 h-4 mr-2" />
          Run Full Analysis
        </Button>
      </div>

      <Tabs defaultValue="upload" className="space-y-6">
        <TabsList>
          <TabsTrigger value="upload">Upload & Analyze</TabsTrigger>
          <TabsTrigger value="results">Analysis Results</TabsTrigger>
          <TabsTrigger value="metrics">Code Metrics</TabsTrigger>
          <TabsTrigger value="suggestions">Optimization</TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-6">
          {/* Code Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="w-5 h-5" />
                Upload ABAP Code
              </CardTitle>
              <CardDescription>Upload your ABAP programs for comprehensive analysis</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                <Upload className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Drop ABAP files here</h3>
                <p className="text-muted-foreground mb-4">or click to browse files</p>
                <Button variant="outline">
                  <FileText className="w-4 h-4 mr-2" />
                  Browse Files
                </Button>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Or paste code directly:</label>
                <Textarea placeholder="Paste your ABAP code here..." className="min-h-[200px] font-mono text-sm" />
              </div>

              <div className="flex gap-2">
                <Button className="flex-1">
                  <Code2 className="w-4 h-4 mr-2" />
                  Analyze Code
                </Button>
                <Button variant="outline">
                  <Search className="w-4 h-4 mr-2" />
                  Quick Scan
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-6">
          {/* Analysis Results */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Code Issues Found
              </CardTitle>
              <CardDescription>Issues detected in your ABAP code with suggestions for improvement</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {codeIssues.map((issue, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        {getIssueIcon(issue.type)}
                        <span className="font-medium">{issue.file}</span>
                        <Badge variant="outline">Line {issue.line}</Badge>
                        <Badge className={getSeverityColor(issue.severity)}>{issue.severity}</Badge>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm">{issue.message}</p>
                      <div className="bg-muted p-3 rounded text-sm">
                        <strong>Suggestion:</strong> {issue.suggestion}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          {/* Code Metrics */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Code Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Total Lines:</span>
                  <span className="font-medium">{codeMetrics.totalLines.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Code Lines:</span>
                  <span className="font-medium">{codeMetrics.codeLines.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Comment Lines:</span>
                  <span className="font-medium">{codeMetrics.commentLines.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Blank Lines:</span>
                  <span className="font-medium">{codeMetrics.blankLines.toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quality Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Cyclomatic Complexity:</span>
                    <Badge
                      variant={
                        codeMetrics.complexity > 20
                          ? "destructive"
                          : codeMetrics.complexity > 10
                            ? "secondary"
                            : "default"
                      }
                    >
                      {codeMetrics.complexity}
                    </Badge>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${codeMetrics.complexity > 20 ? "bg-red-600" : codeMetrics.complexity > 10 ? "bg-yellow-600" : "bg-green-600"}`}
                      style={{ width: `${Math.min(codeMetrics.complexity * 2, 100)}%` }}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Maintainability Index:</span>
                    <Badge
                      variant={
                        codeMetrics.maintainabilityIndex < 50
                          ? "destructive"
                          : codeMetrics.maintainabilityIndex < 70
                            ? "secondary"
                            : "default"
                      }
                    >
                      {codeMetrics.maintainabilityIndex}
                    </Badge>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${codeMetrics.maintainabilityIndex < 50 ? "bg-red-600" : codeMetrics.maintainabilityIndex < 70 ? "bg-yellow-600" : "bg-green-600"}`}
                      style={{ width: `${codeMetrics.maintainabilityIndex}%` }}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Test Coverage:</span>
                    <Badge
                      variant={
                        codeMetrics.testCoverage < 50
                          ? "destructive"
                          : codeMetrics.testCoverage < 80
                            ? "secondary"
                            : "default"
                      }
                    >
                      {codeMetrics.testCoverage}%
                    </Badge>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${codeMetrics.testCoverage < 50 ? "bg-red-600" : codeMetrics.testCoverage < 80 ? "bg-yellow-600" : "bg-green-600"}`}
                      style={{ width: `${codeMetrics.testCoverage}%` }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="suggestions" className="space-y-6">
          {/* Optimization Suggestions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5" />
                Performance Optimization
              </CardTitle>
              <CardDescription>Recommendations to improve code performance and maintainability</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle2 className="w-4 h-4 text-green-600" />
                    <span className="font-medium">Database Access Optimization</span>
                    <Badge variant="outline">High Impact</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Replace individual SELECT statements with bulk operations using SELECT...FOR ALL ENTRIES
                  </p>
                  <div className="bg-muted p-3 rounded text-sm font-mono">
                    SELECT * FROM pa0001 FOR ALL ENTRIES IN lt_pernr WHERE pernr = lt_pernr-pernr
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle2 className="w-4 h-4 text-green-600" />
                    <span className="font-medium">Memory Management</span>
                    <Badge variant="outline">Medium Impact</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Use CLEAR and FREE statements to release memory for large internal tables
                  </p>
                  <div className="bg-muted p-3 rounded text-sm font-mono">
                    CLEAR: lt_large_table. FREE: lt_large_table.
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle2 className="w-4 h-4 text-green-600" />
                    <span className="font-medium">Modern ABAP Syntax</span>
                    <Badge variant="outline">Low Impact</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Use inline declarations and constructor expressions for cleaner code
                  </p>
                  <div className="bg-muted p-3 rounded text-sm font-mono">
                    DATA(lv_result) = VALUE string_table( ( 'Line1' ) ( 'Line2' ) )
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

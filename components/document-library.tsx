"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  FileText,
  Plus,
  Search,
  Download,
  Share,
  Edit,
  Eye,
  Calendar,
  User,
  BookOpen,
  Code2,
  Settings,
  Filter,
} from "lucide-react"
import Link from "next/link"

interface Document {
  id: string
  title: string
  description: string
  type: "specification" | "guide" | "review" | "template" | "api-doc"
  module: string
  author: string
  lastModified: string
  version: string
  status: "draft" | "review" | "published" | "archived"
  downloads: number
  tags: string[]
  teamId?: string
  size: string
}

export function DocumentLibrary() {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState("all")
  const [filterModule, setFilterModule] = useState("all")

  const [documents] = useState<Document[]>([
    {
      id: "doc-1",
      title: "Payroll Schema Enhancement Specification",
      description: "Comprehensive technical specification for global payroll schema improvements",
      type: "specification",
      module: "PY",
      author: "John Smith",
      lastModified: "2024-01-15",
      version: "2.1",
      status: "published",
      downloads: 45,
      tags: ["payroll", "schema", "international", "specification"],
      teamId: "team-1",
      size: "2.4 MB",
    },
    {
      id: "doc-2",
      title: "Time Management API Integration Guide",
      description: "Step-by-step guide for integrating with external time tracking systems",
      type: "guide",
      module: "PT",
      author: "Tom Brown",
      lastModified: "2024-01-14",
      version: "1.8",
      status: "published",
      downloads: 32,
      tags: ["time-management", "api", "integration", "guide"],
      teamId: "team-2",
      size: "1.8 MB",
    },
    {
      id: "doc-3",
      title: "Benefits Calculation Code Review",
      description: "Code review documentation for benefits calculation module updates",
      type: "review",
      module: "PB",
      author: "Rachel Green",
      lastModified: "2024-01-13",
      version: "1.0",
      status: "review",
      downloads: 12,
      tags: ["benefits", "code-review", "calculation"],
      teamId: "team-3",
      size: "856 KB",
    },
    {
      id: "doc-4",
      title: "ABAP Development Standards Template",
      description: "Standard template for ABAP development documentation in HCM projects",
      type: "template",
      module: "General",
      author: "Lisa Wilson",
      lastModified: "2024-01-12",
      version: "3.2",
      status: "published",
      downloads: 89,
      tags: ["template", "standards", "abap", "documentation"],
      size: "1.2 MB",
    },
    {
      id: "doc-5",
      title: "HCM REST API Documentation",
      description: "Complete API reference for HCM module integrations",
      type: "api-doc",
      module: "Integration",
      author: "Mike Davis",
      lastModified: "2024-01-11",
      version: "4.1",
      status: "published",
      downloads: 67,
      tags: ["api", "rest", "integration", "reference"],
      size: "3.1 MB",
    },
    {
      id: "doc-6",
      title: "Employee Data Migration Guide",
      description: "Comprehensive guide for migrating employee data between HCM systems",
      type: "guide",
      module: "PA",
      author: "Sarah Johnson",
      lastModified: "2024-01-10",
      version: "2.0",
      status: "draft",
      downloads: 8,
      tags: ["migration", "employee-data", "pa", "guide"],
      size: "2.7 MB",
    },
  ])

  const documentTypes = [
    { value: "specification", label: "Technical Specifications", icon: FileText },
    { value: "guide", label: "Implementation Guides", icon: BookOpen },
    { value: "review", label: "Code Reviews", icon: Code2 },
    { value: "template", label: "Templates", icon: Settings },
    { value: "api-doc", label: "API Documentation", icon: Code2 },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "published":
        return "bg-green-100 text-green-800"
      case "review":
        return "bg-yellow-100 text-yellow-800"
      case "draft":
        return "bg-blue-100 text-blue-800"
      case "archived":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "specification":
        return "bg-purple-100 text-purple-800"
      case "guide":
        return "bg-blue-100 text-blue-800"
      case "review":
        return "bg-orange-100 text-orange-800"
      case "template":
        return "bg-green-100 text-green-800"
      case "api-doc":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const filteredDocuments = documents.filter((doc) => {
    const matchesSearch =
      doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesType = filterType === "all" || doc.type === filterType
    const matchesModule = filterModule === "all" || doc.module === filterModule
    return matchesSearch && matchesType && matchesModule
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Document Library</h2>
          <p className="text-muted-foreground">Technical documentation and knowledge base for HCM development</p>
        </div>
        <Button asChild>
          <Link href="/documents/new">
            <Plus className="w-4 h-4 mr-2" />
            New Document
          </Link>
        </Button>
      </div>

      {/* Document Stats */}
      <div className="grid gap-4 md:grid-cols-5">
        {documentTypes.map((type) => {
          const count = documents.filter((doc) => doc.type === type.value).length
          return (
            <Card key={type.value}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">{type.label}</p>
                    <p className="text-2xl font-bold">{count}</p>
                  </div>
                  <type.icon className="w-4 h-4 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <Tabs defaultValue="library" className="space-y-6">
        <TabsList>
          <TabsTrigger value="library">Document Library</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="recent">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="library" className="space-y-4">
          {/* Search and Filters */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex gap-4 flex-wrap">
                <div className="flex-1 min-w-[200px]">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <Input
                      placeholder="Search documents, tags, or content..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Document Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="specification">Specifications</SelectItem>
                    <SelectItem value="guide">Guides</SelectItem>
                    <SelectItem value="review">Reviews</SelectItem>
                    <SelectItem value="template">Templates</SelectItem>
                    <SelectItem value="api-doc">API Docs</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={filterModule} onValueChange={setFilterModule}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Module" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Modules</SelectItem>
                    <SelectItem value="PY">Payroll</SelectItem>
                    <SelectItem value="PT">Time Mgmt</SelectItem>
                    <SelectItem value="PB">Benefits</SelectItem>
                    <SelectItem value="PA">Personnel</SelectItem>
                    <SelectItem value="General">General</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline">
                  <Filter className="w-4 h-4 mr-2" />
                  More Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Documents Grid */}
          <div className="grid gap-4">
            {filteredDocuments.map((doc) => (
              <Card key={doc.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="space-y-2 flex-1">
                      <div className="flex items-center gap-2">
                        <FileText className="w-5 h-5 text-muted-foreground" />
                        <h3 className="font-semibold">{doc.title}</h3>
                        <Badge className={getTypeColor(doc.type)}>{doc.type}</Badge>
                        <Badge variant="outline">{doc.module}</Badge>
                        <Badge className={getStatusColor(doc.status)}>{doc.status}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{doc.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {doc.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs px-2 py-0">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex gap-2 ml-4">
                      <Button variant="ghost" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Download className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Share className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        {doc.author}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {doc.lastModified}
                      </div>
                      <div>Version {doc.version}</div>
                      <div>{doc.size}</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span>{doc.downloads} downloads</span>
                      {doc.teamId && (
                        <Badge variant="outline" className="text-xs">
                          Team Doc
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Document Templates</CardTitle>
              <CardDescription>Pre-built templates for common HCM documentation types</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {[
                  {
                    name: "Technical Specification",
                    description: "Standard template for technical specifications",
                    icon: FileText,
                    category: "Development",
                  },
                  {
                    name: "Code Review Checklist",
                    description: "Comprehensive code review checklist template",
                    icon: Code2,
                    category: "Quality",
                  },
                  {
                    name: "API Documentation",
                    description: "Template for REST API documentation",
                    icon: BookOpen,
                    category: "Integration",
                  },
                  {
                    name: "User Guide Template",
                    description: "Template for end-user documentation",
                    icon: BookOpen,
                    category: "User Guide",
                  },
                  {
                    name: "Test Plan Template",
                    description: "Comprehensive test planning template",
                    icon: Settings,
                    category: "Testing",
                  },
                  {
                    name: "Deployment Guide",
                    description: "Template for deployment documentation",
                    icon: Settings,
                    category: "Operations",
                  },
                ].map((template, index) => (
                  <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <template.icon className="w-5 h-5 text-muted-foreground" />
                          <h4 className="font-medium">{template.name}</h4>
                        </div>
                        <p className="text-sm text-muted-foreground">{template.description}</p>
                        <div className="flex items-center justify-between">
                          <Badge variant="outline">{template.category}</Badge>
                          <Button size="sm">
                            <Plus className="w-3 h-3 mr-1" />
                            Use Template
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Document Activity</CardTitle>
              <CardDescription>Latest document updates and activities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    action: "created",
                    document: "Payroll Schema Enhancement Specification",
                    user: "John Smith",
                    time: "2 hours ago",
                    type: "specification",
                  },
                  {
                    action: "updated",
                    document: "Time Management API Integration Guide",
                    user: "Tom Brown",
                    time: "4 hours ago",
                    type: "guide",
                  },
                  {
                    action: "reviewed",
                    document: "Benefits Calculation Code Review",
                    user: "Rachel Green",
                    time: "1 day ago",
                    type: "review",
                  },
                  {
                    action: "published",
                    document: "HCM REST API Documentation",
                    user: "Mike Davis",
                    time: "2 days ago",
                    type: "api-doc",
                  },
                ].map((activity, index) => (
                  <div key={index} className="flex items-center gap-4 p-3 border rounded-lg">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted">
                      <FileText className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">
                        <span className="capitalize">{activity.action}</span> "{activity.document}"
                      </p>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{activity.user}</span>
                        <span>•</span>
                        <Badge variant="outline" className="text-xs">
                          {activity.type}
                        </Badge>
                        <span>•</span>
                        <span>{activity.time}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

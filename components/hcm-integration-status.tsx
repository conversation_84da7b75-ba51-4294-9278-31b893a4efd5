"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import {
  CheckCircle2,
  AlertTriangle,
  RefreshCw,
  Settings,
  Activity,
  Shield,
  Zap,
  Server,
  Globe,
  Lock,
} from "lucide-react"

interface HCMSystem {
  id: string
  name: string
  environment: "DEV" | "QAS" | "PRD"
  client: string
  status: "online" | "offline" | "maintenance" | "error"
  health: number
  lastSync: string
  modules: string[]
  version: string
  uptime: string
  responseTime: number
  activeUsers: number
}

interface IntegrationService {
  id: string
  name: string
  description: string
  status: "active" | "inactive" | "error"
  lastRun: string
  successRate: number
  avgDuration: string
  module: string
}

export function HCMIntegrationStatus() {
  const [systems, setSystems] = useState<HCMSystem[]>([
    {
      id: "hcm-prd",
      name: "SAP HCM Production",
      environment: "PRD",
      client: "100",
      status: "online",
      health: 98,
      lastSync: "2 minutes ago",
      modules: ["PA", "PT", "PY", "PB", "RC"],
      version: "EHP8",
      uptime: "99.9%",
      responseTime: 45,
      activeUsers: 247,
    },
    {
      id: "hcm-qas",
      name: "SAP HCM Quality Assurance",
      environment: "QAS",
      client: "200",
      status: "online",
      health: 95,
      lastSync: "5 minutes ago",
      modules: ["PA", "PT", "PY", "PB"],
      version: "EHP8",
      uptime: "99.5%",
      responseTime: 52,
      activeUsers: 12,
    },
    {
      id: "hcm-dev",
      name: "SAP HCM Development",
      environment: "DEV",
      client: "300",
      status: "maintenance",
      health: 85,
      lastSync: "1 hour ago",
      modules: ["PA", "PT", "PY"],
      version: "EHP8",
      uptime: "98.2%",
      responseTime: 78,
      activeUsers: 8,
    },
  ])

  const [integrationServices] = useState<IntegrationService[]>([
    {
      id: "employee-sync",
      name: "Employee Data Synchronization",
      description: "Real-time sync of employee master data across systems",
      status: "active",
      lastRun: "5 minutes ago",
      successRate: 99.2,
      avgDuration: "2.3s",
      module: "PA",
    },
    {
      id: "payroll-export",
      name: "Payroll Data Export",
      description: "Automated export of payroll results to external systems",
      status: "active",
      lastRun: "1 hour ago",
      successRate: 98.7,
      avgDuration: "45s",
      module: "PY",
    },
    {
      id: "time-import",
      name: "Time Data Import",
      description: "Import time entries from external time tracking systems",
      status: "active",
      lastRun: "15 minutes ago",
      successRate: 97.5,
      avgDuration: "12s",
      module: "PT",
    },
    {
      id: "benefits-calc",
      name: "Benefits Calculation Service",
      description: "Real-time benefits calculation and validation",
      status: "error",
      lastRun: "2 hours ago",
      successRate: 85.2,
      avgDuration: "8.7s",
      module: "PB",
    },
  ])

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setSystems((prev) =>
        prev.map((system) => ({
          ...system,
          health: Math.max(80, Math.min(100, system.health + (Math.random() - 0.5) * 2)),
          responseTime: Math.max(20, system.responseTime + (Math.random() - 0.5) * 10),
          activeUsers: Math.max(0, system.activeUsers + Math.floor((Math.random() - 0.5) * 5)),
        })),
      )
    }, 10000)

    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "online":
      case "active":
        return <CheckCircle2 className="w-4 h-4 text-green-600" />
      case "offline":
      case "error":
        return <AlertTriangle className="w-4 h-4 text-red-600" />
      case "maintenance":
        return <RefreshCw className="w-4 h-4 text-yellow-600" />
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
      case "active":
        return "bg-green-100 text-green-800"
      case "offline":
      case "error":
        return "bg-red-100 text-red-800"
      case "maintenance":
      case "inactive":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getHealthColor = (health: number) => {
    if (health >= 95) return "text-green-600"
    if (health >= 85) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">SAP HCM Integration Status</h2>
          <p className="text-muted-foreground">Real-time monitoring of HCM system connections and services</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh All
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      {/* System Overview */}
      <div className="grid gap-4 md:grid-cols-3">
        {systems.map((system) => (
          <Card key={system.id} className="relative overflow-hidden">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Server className="w-4 h-4" />
                  <CardTitle className="text-base">{system.name}</CardTitle>
                </div>
                <Badge className={getStatusColor(system.status)}>{system.status}</Badge>
              </div>
              <CardDescription>
                {system.environment} • Client {system.client} • {system.version}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>System Health</span>
                  <span className={`font-medium ${getHealthColor(system.health)}`}>{system.health}%</span>
                </div>
                <Progress value={system.health} className="h-2" />
              </div>

              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <p className="text-muted-foreground">Response Time</p>
                  <p className="font-medium">{system.responseTime}ms</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Active Users</p>
                  <p className="font-medium">{system.activeUsers}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Uptime</p>
                  <p className="font-medium">{system.uptime}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Last Sync</p>
                  <p className="font-medium">{system.lastSync}</p>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Active Modules</p>
                <div className="flex flex-wrap gap-1">
                  {system.modules.map((module) => (
                    <Badge key={module} variant="secondary" className="text-xs">
                      {module}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
            <div
              className={`absolute bottom-0 left-0 w-full h-1 ${
                system.status === "online"
                  ? "bg-green-500"
                  : system.status === "maintenance"
                    ? "bg-yellow-500"
                    : "bg-red-500"
              }`}
            />
          </Card>
        ))}
      </div>

      <Tabs defaultValue="services" className="space-y-6">
        <TabsList>
          <TabsTrigger value="services">Integration Services</TabsTrigger>
          <TabsTrigger value="monitoring">System Monitoring</TabsTrigger>
          <TabsTrigger value="security">Security & Compliance</TabsTrigger>
          <TabsTrigger value="performance">Performance Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="services" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5" />
                Integration Services
              </CardTitle>
              <CardDescription>Automated data synchronization and integration services</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {integrationServices.map((service) => (
                  <div key={service.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(service.status)}
                          <h4 className="font-medium">{service.name}</h4>
                          <Badge className={getStatusColor(service.status)}>{service.status}</Badge>
                          <Badge variant="outline">{service.module}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{service.description}</p>
                      </div>
                      <Button variant="outline" size="sm">
                        <Settings className="w-3 h-3 mr-1" />
                        Configure
                      </Button>
                    </div>

                    <div className="grid gap-4 md:grid-cols-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Success Rate</p>
                        <p className="font-medium">{service.successRate}%</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Avg Duration</p>
                        <p className="font-medium">{service.avgDuration}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Last Run</p>
                        <p className="font-medium">{service.lastRun}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Status</p>
                        <p className="font-medium capitalize">{service.status}</p>
                      </div>
                    </div>

                    <div className="mt-3 space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Success Rate</span>
                        <span>{service.successRate}%</span>
                      </div>
                      <Progress value={service.successRate} className="h-1" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  System Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {systems.map((system) => (
                  <div key={system.id} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{system.name}</span>
                      <span className={getHealthColor(system.health)}>{system.health}%</span>
                    </div>
                    <Progress value={system.health} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  Network Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Network Latency</span>
                  <Badge className="bg-green-100 text-green-800">Low (12ms)</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Bandwidth Usage</span>
                  <Badge className="bg-blue-100 text-blue-800">Normal (45%)</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Connection Pool</span>
                  <Badge className="bg-green-100 text-green-800">Healthy</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Load Balancer</span>
                  <Badge className="bg-green-100 text-green-800">Active</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Security Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">SSL/TLS Encryption</span>
                  <Badge className="bg-green-100 text-green-800">Enabled</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">API Authentication</span>
                  <Badge className="bg-green-100 text-green-800">OAuth 2.0</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Data Encryption</span>
                  <Badge className="bg-green-100 text-green-800">AES-256</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Access Control</span>
                  <Badge className="bg-green-100 text-green-800">Role-based</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Audit Logging</span>
                  <Badge className="bg-green-100 text-green-800">Active</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lock className="w-5 h-5" />
                  Compliance Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">GDPR Compliance</span>
                  <Badge className="bg-green-100 text-green-800">Compliant</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">SOX Compliance</span>
                  <Badge className="bg-green-100 text-green-800">Compliant</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Data Retention</span>
                  <Badge className="bg-blue-100 text-blue-800">7 Years</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Vulnerability Scan</span>
                  <Badge className="bg-green-100 text-green-800">Clean</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Penetration Test</span>
                  <Badge className="bg-yellow-100 text-yellow-800">Due Soon</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Response Times</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {systems.map((system) => (
                  <div key={system.id} className="flex justify-between items-center">
                    <span className="text-sm">{system.environment}</span>
                    <Badge variant="outline">{system.responseTime}ms</Badge>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Load</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">CPU Usage</span>
                  <Badge className="bg-blue-100 text-blue-800">23%</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Memory Usage</span>
                  <Badge className="bg-green-100 text-green-800">67%</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Disk I/O</span>
                  <Badge className="bg-yellow-100 text-yellow-800">45%</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Network I/O</span>
                  <Badge className="bg-green-100 text-green-800">12%</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Throughput</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Requests/min</span>
                  <Badge variant="outline">1,247</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Transactions/sec</span>
                  <Badge variant="outline">45</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Error Rate</span>
                  <Badge className="bg-green-100 text-green-800">0.02%</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Availability</span>
                  <Badge className="bg-green-100 text-green-800">99.9%</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

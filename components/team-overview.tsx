"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Users, Plus, Calendar, Target, GitBranch, MessageSquare } from "lucide-react"
import Link from "next/link"

interface TeamMember {
  id: string
  name: string
  role: string
  avatar: string
  initials: string
  expertise: string[]
}

interface Team {
  id: string
  name: string
  description: string
  module: string
  status: "active" | "planning" | "completed"
  progress: number
  members: TeamMember[]
  tasksCompleted: number
  totalTasks: number
  createdDate: string
  deadline: string
  lead: string
}

export function TeamOverview() {
  const [teams] = useState<Team[]>([
    {
      id: "team-1",
      name: "Payroll Enhancement Squad",
      description: "Implementing advanced payroll calculation features for global operations",
      module: "PY",
      status: "active",
      progress: 75,
      members: [
        {
          id: "1",
          name: "<PERSON>",
          role: "Team Lead",
          avatar: "/placeholder.svg?height=32&width=32",
          initials: "JS",
          expertise: ["Payroll", "ABAP", "Integration"],
        },
        {
          id: "2",
          name: "Sarah Johnson",
          role: "Senior Developer",
          avatar: "/placeholder.svg?height=32&width=32",
          initials: "SJ",
          expertise: ["Schema Design", "Testing"],
        },
        {
          id: "3",
          name: "Mike Davis",
          role: "ABAP Developer",
          avatar: "/placeholder.svg?height=32&width=32",
          initials: "MD",
          expertise: ["ABAP", "Performance"],
        },
        {
          id: "4",
          name: "Lisa Wilson",
          role: "Functional Analyst",
          avatar: "/placeholder.svg?height=32&width=32",
          initials: "LW",
          expertise: ["Requirements", "Testing"],
        },
      ],
      tasksCompleted: 18,
      totalTasks: 24,
      createdDate: "2024-01-08",
      deadline: "2024-02-15",
      lead: "John Smith",
    },
    {
      id: "team-2",
      name: "Time Management Innovation",
      description: "Modernizing time recording and attendance management systems",
      module: "PT",
      status: "active",
      progress: 60,
      members: [
        {
          id: "5",
          name: "Tom Brown",
          role: "Team Lead",
          avatar: "/placeholder.svg?height=32&width=32",
          initials: "TB",
          expertise: ["Time Management", "API Design"],
        },
        {
          id: "6",
          name: "Emma Wilson",
          role: "UI/UX Developer",
          avatar: "/placeholder.svg?height=32&width=32",
          initials: "EW",
          expertise: ["Frontend", "User Experience"],
        },
        {
          id: "7",
          name: "Alex Chen",
          role: "Backend Developer",
          avatar: "/placeholder.svg?height=32&width=32",
          initials: "AC",
          expertise: ["ABAP", "Database"],
        },
      ],
      tasksCompleted: 12,
      totalTasks: 20,
      createdDate: "2024-01-10",
      deadline: "2024-02-28",
      lead: "Tom Brown",
    },
    {
      id: "team-3",
      name: "Benefits Administration Revamp",
      description: "Complete overhaul of benefits enrollment and calculation processes",
      module: "PB",
      status: "planning",
      progress: 25,
      members: [
        {
          id: "8",
          name: "Rachel Green",
          role: "Team Lead",
          avatar: "/placeholder.svg?height=32&width=32",
          initials: "RG",
          expertise: ["Benefits", "Process Design"],
        },
        {
          id: "9",
          name: "David Kim",
          role: "ABAP Developer",
          avatar: "/placeholder.svg?height=32&width=32",
          initials: "DK",
          expertise: ["ABAP", "Integration"],
        },
      ],
      tasksCompleted: 3,
      totalTasks: 12,
      createdDate: "2024-01-15",
      deadline: "2024-03-30",
      lead: "Rachel Green",
    },
  ])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "planning":
        return "bg-blue-100 text-blue-800"
      case "completed":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getModuleColor = (module: string) => {
    switch (module) {
      case "PY":
        return "bg-purple-100 text-purple-800"
      case "PT":
        return "bg-blue-100 text-blue-800"
      case "PB":
        return "bg-orange-100 text-orange-800"
      case "PA":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Development Teams</h2>
          <p className="text-muted-foreground">Collaborative HCM development workgroups</p>
        </div>
        <Button asChild>
          <Link href="/teams/new">
            <Plus className="w-4 h-4 mr-2" />
            Create Team
          </Link>
        </Button>
      </div>

      {/* Team Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Teams</p>
                <p className="text-2xl font-bold">{teams.filter((t) => t.status === "active").length}</p>
              </div>
              <Users className="w-4 h-4 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Members</p>
                <p className="text-2xl font-bold">{teams.reduce((sum, team) => sum + team.members.length, 0)}</p>
              </div>
              <Users className="w-4 h-4 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Tasks Completed</p>
                <p className="text-2xl font-bold">{teams.reduce((sum, team) => sum + team.tasksCompleted, 0)}</p>
              </div>
              <Target className="w-4 h-4 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Progress</p>
                <p className="text-2xl font-bold">
                  {Math.round(teams.reduce((sum, team) => sum + team.progress, 0) / teams.length)}%
                </p>
              </div>
              <Progress
                value={teams.reduce((sum, team) => sum + team.progress, 0) / teams.length}
                className="w-8 h-2"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Teams List */}
      <div className="grid gap-6">
        {teams.map((team) => (
          <Card key={team.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-lg">{team.name}</CardTitle>
                    <Badge className={getModuleColor(team.module)}>{team.module}</Badge>
                    <Badge className={getStatusColor(team.status)}>{team.status}</Badge>
                  </div>
                  <CardDescription>{team.description}</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <MessageSquare className="w-3 h-3 mr-1" />
                    Chat
                  </Button>
                  <Button variant="outline" size="sm">
                    <GitBranch className="w-3 h-3 mr-1" />
                    Repository
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Progress */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Team Progress</span>
                  <span>
                    {team.tasksCompleted}/{team.totalTasks} tasks ({team.progress}%)
                  </span>
                </div>
                <Progress value={team.progress} className="h-2" />
              </div>

              {/* Team Members */}
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Team Members ({team.members.length})
                </h4>
                <div className="grid gap-3 md:grid-cols-2">
                  {team.members.map((member) => (
                    <div key={member.id} className="flex items-center gap-3 p-3 border rounded-lg">
                      <Avatar className="w-8 h-8">
                        <AvatarImage src={member.avatar || "/placeholder.svg"} />
                        <AvatarFallback className="text-xs">{member.initials}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <p className="font-medium text-sm">{member.name}</p>
                        <p className="text-xs text-muted-foreground">{member.role}</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {member.expertise.slice(0, 2).map((skill) => (
                            <Badge key={skill} variant="secondary" className="text-xs px-1 py-0">
                              {skill}
                            </Badge>
                          ))}
                          {member.expertise.length > 2 && (
                            <Badge variant="secondary" className="text-xs px-1 py-0">
                              +{member.expertise.length - 2}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Team Info */}
              <div className="flex items-center justify-between text-sm text-muted-foreground pt-2 border-t">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    <span>Created: {team.createdDate}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Target className="w-3 h-3" />
                    <span>Deadline: {team.deadline}</span>
                  </div>
                </div>
                <span>Lead: {team.lead}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

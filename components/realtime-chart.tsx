"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, TrendingDown, Activity } from "lucide-react"

interface ChartData {
  time: string
  tasks: number
  commits: number
  deployments: number
  codeReviews: number
}

export function RealtimeChart() {
  const [data, setData] = useState<ChartData[]>([
    { time: "00:00", tasks: 12, commits: 5, deployments: 1, codeReviews: 3 },
    { time: "04:00", tasks: 8, commits: 2, deployments: 0, codeReviews: 1 },
    { time: "08:00", tasks: 25, commits: 12, deployments: 2, codeReviews: 8 },
    { time: "12:00", tasks: 32, commits: 18, deployments: 3, codeReviews: 12 },
    { time: "16:00", tasks: 28, commits: 15, deployments: 1, codeReviews: 9 },
    { time: "20:00", tasks: 15, commits: 8, deployments: 0, codeReviews: 4 },
  ])

  const [metrics, setMetrics] = useState({
    totalTasks: 120,
    avgCompletionTime: 4.2,
    codeQuality: 94,
    deploymentSuccess: 98.5,
    teamVelocity: 85,
  })

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date()
      const timeStr = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}`

      setData((prev) => {
        const newData = [
          ...prev.slice(1),
          {
            time: timeStr,
            tasks: Math.floor(Math.random() * 30) + 10,
            commits: Math.floor(Math.random() * 20) + 5,
            deployments: Math.floor(Math.random() * 4),
            codeReviews: Math.floor(Math.random() * 15) + 3,
          },
        ]
        return newData
      })

      setMetrics((prev) => ({
        ...prev,
        codeQuality: Math.max(90, Math.min(100, prev.codeQuality + (Math.random() - 0.5) * 2)),
        teamVelocity: Math.max(70, Math.min(100, prev.teamVelocity + (Math.random() - 0.5) * 5)),
      }))
    }, 10000)

    return () => clearInterval(interval)
  }, [])

  const maxTasks = Math.max(...data.map((d) => d.tasks))
  const maxCommits = Math.max(...data.map((d) => d.commits))

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Tasks</p>
                <p className="text-2xl font-bold">{metrics.totalTasks}</p>
              </div>
              <TrendingUp className="w-4 h-4 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Completion</p>
                <p className="text-2xl font-bold">{metrics.avgCompletionTime}d</p>
              </div>
              <TrendingDown className="w-4 h-4 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Code Quality</p>
                <p className="text-2xl font-bold">{metrics.codeQuality.toFixed(1)}%</p>
              </div>
              <Badge className="bg-green-100 text-green-800">Excellent</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Deploy Success</p>
                <p className="text-2xl font-bold">{metrics.deploymentSuccess}%</p>
              </div>
              <Badge className="bg-green-100 text-green-800">High</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Team Velocity</p>
                <p className="text-2xl font-bold">{metrics.teamVelocity.toFixed(0)}%</p>
              </div>
              <Activity className="w-4 h-4 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Development Activity (Last 24 Hours)</CardTitle>
          <CardDescription>Real-time development metrics and team activity</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Tasks Chart */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Task Completion</span>
                <Badge variant="outline">Live</Badge>
              </div>
              <div className="flex items-end gap-2 h-20">
                {data.map((point, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center gap-1">
                    <div
                      className="w-full bg-blue-500 rounded-t transition-all duration-500"
                      style={{ height: `${(point.tasks / maxTasks) * 100}%` }}
                    />
                    <span className="text-xs text-muted-foreground">{point.time}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Commits Chart */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Code Commits</span>
                <Badge variant="outline">Live</Badge>
              </div>
              <div className="flex items-end gap-2 h-16">
                {data.map((point, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center gap-1">
                    <div
                      className="w-full bg-green-500 rounded-t transition-all duration-500"
                      style={{ height: `${(point.commits / maxCommits) * 100}%` }}
                    />
                    <span className="text-xs text-muted-foreground">{point.time}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Legend */}
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded" />
                <span>Tasks Completed</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded" />
                <span>Code Commits</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span>Real-time Updates</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Calendar, Clock, User, AlertTriangle, CheckCircle2, Play, Pause } from "lucide-react"
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd"
import Link from "next/link"

interface Task {
  id: string
  title: string
  description: string
  customer: string
  priority: "Critical" | "High" | "Medium" | "Low"
  module: string
  assignee: {
    name: string
    avatar: string
    initials: string
  }
  dueDate: string
  estimatedHours: number
  completedHours: number
  tags: string[]
  status: "todo" | "inprogress" | "testing" | "completed"
}

const initialTasks: Task[] = [
  {
    id: "task-1",
    title: "Payroll Schema Enhancement",
    description: "Implement custom payroll schema for international payroll processing",
    customer: "Global Corp",
    priority: "Critical",
    module: "PY",
    assignee: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "JS",
    },
    dueDate: "2024-01-15",
    estimatedHours: 40,
    completedHours: 30,
    tags: ["payroll", "schema", "international"],
    status: "inprogress",
  },
  {
    id: "task-2",
    title: "Time Management API",
    description: "Develop REST API for time recording integration",
    customer: "Tech Solutions",
    priority: "High",
    module: "PT",
    assignee: {
      name: "Sarah Johnson",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "SJ",
    },
    dueDate: "2024-01-18",
    estimatedHours: 32,
    completedHours: 28,
    tags: ["api", "integration", "time"],
    status: "testing",
  },
  {
    id: "task-3",
    title: "Benefits Calculation Logic",
    description: "Update benefits calculation for new healthcare plans",
    customer: "Healthcare Inc",
    priority: "Medium",
    module: "PB",
    assignee: {
      name: "Mike Davis",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "MD",
    },
    dueDate: "2024-01-22",
    estimatedHours: 24,
    completedHours: 5,
    tags: ["benefits", "healthcare", "calculation"],
    status: "todo",
  },
  {
    id: "task-4",
    title: "Employee Self-Service Portal",
    description: "Mobile-friendly ESS portal for leave requests",
    customer: "Mobile First",
    priority: "High",
    module: "PA",
    assignee: {
      name: "Lisa Wilson",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "LW",
    },
    dueDate: "2024-01-25",
    estimatedHours: 48,
    completedHours: 48,
    tags: ["mobile", "ess", "portal"],
    status: "completed",
  },
]

const columns = [
  { id: "todo", title: "To Do", color: "bg-gray-100" },
  { id: "inprogress", title: "In Progress", color: "bg-blue-100" },
  { id: "testing", title: "Testing", color: "bg-yellow-100" },
  { id: "completed", title: "Completed", color: "bg-green-100" },
]

export function TaskKanban() {
  const [tasks, setTasks] = useState(initialTasks)

  const handleDragEnd = (result: any) => {
    if (!result.destination) return

    const { source, destination, draggableId } = result

    if (source.droppableId === destination.droppableId) return

    const updatedTasks = tasks.map((task) =>
      task.id === draggableId ? { ...task, status: destination.droppableId as Task["status"] } : task,
    )

    setTasks(updatedTasks)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800 border-red-200"
      case "High":
        return "bg-orange-100 text-orange-800 border-orange-200"
      case "Medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "Low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "Critical":
        return <AlertTriangle className="w-3 h-3" />
      case "High":
        return <AlertTriangle className="w-3 h-3" />
      default:
        return null
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="w-4 h-4 text-green-600" />
      case "inprogress":
        return <Play className="w-4 h-4 text-blue-600" />
      case "testing":
        return <Pause className="w-4 h-4 text-yellow-600" />
      default:
        return <Clock className="w-4 h-4 text-gray-600" />
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Task Board</span>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              Filter
            </Button>
            <Button variant="outline" size="sm">
              Sort
            </Button>
            <Button size="sm" asChild>
              <Link href="/tasks/new">Add Task</Link>
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <DragDropContext onDragEnd={handleDragEnd}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {columns.map((column) => (
              <div key={column.id} className="space-y-4">
                <div className={`p-3 rounded-lg ${column.color}`}>
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium flex items-center gap-2">
                      {getStatusIcon(column.id)}
                      {column.title}
                    </h3>
                    <Badge variant="secondary">{tasks.filter((task) => task.status === column.id).length}</Badge>
                  </div>
                </div>

                <Droppable droppableId={column.id}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={`space-y-3 min-h-[200px] p-2 rounded-lg transition-colors ${
                        snapshot.isDraggingOver ? "bg-muted/50" : ""
                      }`}
                    >
                      {tasks
                        .filter((task) => task.status === column.id)
                        .map((task, index) => (
                          <Draggable key={task.id} draggableId={task.id} index={index}>
                            {(provided, snapshot) => (
                              <Card
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                className={`cursor-move transition-shadow hover:shadow-md ${
                                  snapshot.isDragging ? "shadow-lg rotate-2" : ""
                                }`}
                              >
                                <CardContent className="p-4 space-y-3">
                                  <div className="flex items-start justify-between">
                                    <Badge className={getPriorityColor(task.priority)} variant="outline">
                                      <div className="flex items-center gap-1">
                                        {getPriorityIcon(task.priority)}
                                        {task.priority}
                                      </div>
                                    </Badge>
                                    <Badge variant="outline">{task.module}</Badge>
                                  </div>

                                  <div>
                                    <h4 className="font-medium text-sm mb-1">{task.title}</h4>
                                    <p className="text-xs text-muted-foreground line-clamp-2">{task.description}</p>
                                  </div>

                                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <User className="w-3 h-3" />
                                    <span>{task.customer}</span>
                                  </div>

                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                      <Avatar className="w-6 h-6">
                                        <AvatarImage src={task.assignee.avatar || "/placeholder.svg"} />
                                        <AvatarFallback className="text-xs">{task.assignee.initials}</AvatarFallback>
                                      </Avatar>
                                      <span className="text-xs text-muted-foreground">{task.assignee.name}</span>
                                    </div>
                                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                      <Calendar className="w-3 h-3" />
                                      {task.dueDate}
                                    </div>
                                  </div>

                                  <div className="space-y-1">
                                    <div className="flex justify-between text-xs">
                                      <span>Progress</span>
                                      <span>{Math.round((task.completedHours / task.estimatedHours) * 100)}%</span>
                                    </div>
                                    <Progress
                                      value={(task.completedHours / task.estimatedHours) * 100}
                                      className="h-1"
                                    />
                                    <div className="text-xs text-muted-foreground">
                                      {task.completedHours}h / {task.estimatedHours}h
                                    </div>
                                  </div>

                                  <div className="flex flex-wrap gap-1">
                                    {task.tags.slice(0, 3).map((tag) => (
                                      <Badge key={tag} variant="secondary" className="text-xs px-1 py-0">
                                        {tag}
                                      </Badge>
                                    ))}
                                    {task.tags.length > 3 && (
                                      <Badge variant="secondary" className="text-xs px-1 py-0">
                                        +{task.tags.length - 3}
                                      </Badge>
                                    )}
                                  </div>
                                </CardContent>
                              </Card>
                            )}
                          </Draggable>
                        ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </div>
            ))}
          </div>
        </DragDropContext>
      </CardContent>
    </Card>
  )
}

"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Home, CheckSquare, Code2, FileText, GitBranch, Database, Settings, Users, BarChart3, Brain } from "lucide-react"

const navigation = [
  { name: "Dashboard", href: "/", icon: Home },
  { name: "Tasks", href: "/tasks", icon: CheckSquare },
  { name: "Teams", href: "/teams", icon: Users },
  { name: "Documents", href: "/documents", icon: FileText },
  { name: "AI Assistant", href: "/ai-assistant", icon: Brain },
  { name: "Code Analysis", href: "/code-analysis", icon: Code2 },
  { name: "Version Control", href: "/version-control", icon: GitBranch },
  { name: "HCM Integration", href: "/integration", icon: Database },
  { name: "Analytics", href: "/analytics", icon: <PERSON><PERSON><PERSON><PERSON> },
  { name: "Settings", href: "/settings", icon: Settings },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="w-64 bg-card border-r border-border">
      <div className="p-6">
        <div className="flex items-center gap-2">
          <Database className="w-8 h-8 text-primary" />
          <div>
            <h2 className="text-lg font-semibold">SAP HCM Portal</h2>
            <p className="text-xs text-muted-foreground">ABAP Development</p>
          </div>
        </div>
      </div>

      <nav className="px-4 space-y-1">
        {navigation.map((item) => (
          <Button
            key={item.name}
            variant={pathname === item.href ? "secondary" : "ghost"}
            className={cn("w-full justify-start gap-3", pathname === item.href && "bg-secondary")}
            asChild
          >
            <Link href={item.href}>
              <item.icon className="w-4 h-4" />
              {item.name}
            </Link>
          </Button>
        ))}
      </nav>
    </div>
  )
}
